---
tools_root: C:\Program Files\IAR Systems\Embedded Workbench 5.3 MSP430\
core_root: &1
- C:\Program Files\IAR Systems\Embedded Workbench 5.3 MSP430\
- 430\
core_bin: &2
- *1
- bin\
core_config: &4
- *1
- config\
core_lib: &3
- *1
- lib\
core_inc: &5
- *1
- inc\
colour: true
:unity:
  :plugins: []
:tools:
  :test_compiler:
    :name: compiler
    :executable:
    - *2
    - icc430.exe
    :arguments:
    - "--dlib_config"
    - - *3
      - dlib\dl430fn.h
    - "--no_cse"
    - "--no_unroll"
    - "--no_inline"
    - "--no_code_motion"
    - "--no_tbaa"
    - "--debug"
    - "-e"
    - "-Ol"
    - "--multiplier=16"
    - "--double=32"
    - "--diag_suppress Pa050"
    - "--diag_suppress Pe111"
    - '-I"$": COLLECTION_PATHS_TEST_TOOLCHAIN_INCLUDE'
    - '-I"$": COLLECTION_PATHS_TEST_SUPPORT_SOURCE_INCLUDE_VENDOR'
    - "-D$: COLLECTION_DEFINES_TEST_AND_VENDOR"
    - "${1}"
    - "-o ${2}"
  :test_linker:
    :name: linker
    :executable:
    - *2
    - xlink.exe
    :arguments:
    - "${1}"
    - "-rt"
    - - *3
      - dlib\dl430fn.r43
    - "-e_PrintfTiny=_Printf"
    - "-e_ScanfSmall=_Scanf"
    - "-s __program_start"
    - "-D_STACK_SIZE=50"
    - "-D_DATA16_HEAP_SIZE=50"
    - "-D_DATA20_HEAP_SIZE=50"
    - "-f"
    - - *4
      - lnk430f5438.xcl
    - "-f"
    - - *4
      - multiplier.xcl
    - "-o ${2}"
  :test_fixture:
    :name: simulator
    :executable:
    - C:\Program Files\IAR Systems\Embedded Workbench 5.3 MSP430\
    - common\bin\CSpyBat.exe
    :arguments:
    - "--silent"
    - - *2
      - 430proc.dll
    - - *2
      - 430sim.dll
    - "${1}"
    - "--plugin"
    - - *2
      - 430bat.dll
    - "--backend -B"
    - "--cpu MSP430F5438"
    - "-p"
    - - *4
      - MSP430F5438.ddf
    - "-d sim"
:extension:
  :object: ".r43"
  :executable: ".d79"
:paths:
  :test:
    - *5
    - - *5
      - dlib
    - - *3
      - dlib
    - src\
    - "../src/"
    - testdata/
    - tests\
    - vendor\unity\src
:defines:
  :test:
  - __MSP430F149__
  - INT_WIDTH=16
  - UNITY_EXCLUDE_FLOAT
  - UNITY_SUPPORT_TEST_CASES
