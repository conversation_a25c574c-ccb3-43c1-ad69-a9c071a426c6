---
:cmock:
  :plugins: []
  :includes:
  - Types.h
  :suite_teardown: |
    if (num_failures)
      _FAILED_TEST();
    else
      _PASSED_TESTS();
    return 0;
colour: true
:tools:
  :test_compiler:
    :name: compiler
    :executable: cd build && picc18
    :arguments:
    - "--chip=18F87J10"
    - "--ide=hitide"
    - "--q"
    - "--asmlist"
    - "--codeoffset=0"
    - "--emi=wordwrite"
    - "--warn=0"
    - "--errors=10"
    - "--char=unsigned"
    - "-Bl"
    - "-G"
    - "--cp=16"
    - "--double=24"
    - "-N255"
    - "--opt=none"
    - "-c"
    - "-M"
    - '-I"$": COLLECTION_PATHS_TEST_TOOLCHAIN_INCLUDE'
    - '-I"$": COLLECTION_PATHS_TEST_SUPPORT_SOURCE_INCLUDE_VENDOR'
    - "-D$: COLLECTION_DEFINES_TEST_AND_VENDOR"
    - "${1}"
    - " ${2}"
  :test_linker:
    :name: linker
    :executable: cd build && picc18
    :arguments:
    - "${1}"
    - "--chip=18F87J10"
    - "--ide=hitide"
    - "--cp=24"
    - "--double=24"
    - "-Lw"
    - "--summary=mem,file"
    - "--summary=+psect"
    - "--summary=+hex"
    - "--output=+intel"
    - "--output=+mcof"
    - "--runtime=+init"
    - "--runtime=+clear"
    - "--runtime=+clib"
    - "--runtime=+keep"
    - "-G"
    - "-MIWasTheLastToBuild.map"
    - "--warn=0"
    - "-Bl"
    - "-O ${2}"
  :test_fixture:
    :name: simulator
    :executable: 'java -client -jar '
    :arguments:
    - - C:\Program Files\HI-TECH Software\HI-TIDE\3.15\lib\
      - simpic18.jar
    - 18F87J10
    - "${1}"
:extension:
  :object: ".obj"
  :executable: ".hex"
:paths:
  :test:
    - c:/Projects/NexGen/Prototypes/CMockTest/src/
    - c:/Projects/NexGen/Prototypes/CMockTest/mocks/
    - c:/CMock/src/
    - c:/CMock/examples/src/
    - c:/CMock/vendor/unity/src/
    - c:/CMock/vendor/unity/examples/helper/
    - tests\
:defines:
  :test:
  - UNITY_INT_WIDTH=16
  - UNITY_POINTER_WIDTH=16
  - CMOCK_MEM_STATIC
  - CMOCK_MEM_SIZE=3000
  - UNITY_SUPPORT_TEST_CASES
  - _PICC18
