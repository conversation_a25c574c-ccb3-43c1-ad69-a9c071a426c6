tools_root: &tools_root 'C:\Program Files\IAR Systems\Embedded Workbench 5.3\'
compiler:
  path:             [*tools_root, 'arm\bin\iccarm.exe']
  source_path:     &systest_generated_path './system/generated/'
  unit_tests_path: &unit_tests_path '../examples/test/'
  mocks_path:      &systest_mocks_path './system/generated/'
  build_path:      &systest_build_path './system/build/'
  options:
    - --dlib_config
    - [*tools_root, 'arm\inc\DLib_Config_Normal.h']
    - --no_cse
    - --no_unroll
    - --no_inline
    - --no_code_motion
    - --no_tbaa
    - --no_clustering
    - --no_scheduling
    - --debug
    - --cpu_mode thumb
    - --endian=little
    - --cpu=ARM7TDMI
    - --interwork
    - --warnings_are_errors
    - --fpu=None
    - -e
    - -On
    #We are supressing some warnings here because we test CMock against some questionable code to make sure it still works
    - --diag_suppress=Pa050
    - --diag_suppress=Pe191
    - --diag_suppress=Pe494
    - --diag_suppress=Pe083
  includes:
    prefix: '-I'
    items:
      - [*tools_root, 'arm\inc\']
      - *systest_generated_path
      - *unit_tests_path
      - *systest_mocks_path
      - '../src/'
      - '../vendor/unity/src/'
      - '../vendor/c_exception/lib/'
      - './system/test_compilation/'
      - './'
      - '.\iar\iar_v5\incIAR\'
  defines:
    prefix: '-D'
    items:
      - CMOCK
  object_files:
    prefix: '-o'
    extension: '.r79'
    destination: *systest_build_path

linker:
  path: [*tools_root, 'arm\bin\ilinkarm.exe']
  options:
    - --redirect _Printf=_PrintfLarge
    - --redirect _Scanf=_ScanfSmall
    - --semihosting
    - --entry __iar_program_start
    - --config iar\iar_v5\Resource\at91SAM7X256_RAM.icf
  object_files:
    path: *systest_build_path
    extension: '.o'
  bin_files:
    prefix: '-o'
    extension: '.out'
    destination: *systest_build_path

simulator:
  path: [*tools_root, 'common\bin\CSpyBat.exe']
  pre_support:
    - --silent
    - [*tools_root, 'arm\bin\armproc.dll']
    - [*tools_root, 'arm\bin\armsim.dll']
  post_support:
    - --plugin
    - [*tools_root, 'arm\bin\armbat.dll']
    - --macro
    - 'iar\iar_v5\Resource\SAM7_SIM.mac'
    - --backend
    - -B
    - -p
    - [*tools_root, 'arm\config\debugger\Atmel\ioat91sam7X256.ddf']
    - -d
    - sim

unsupported:
  - out_of_memory
  - nonstandard_parsed_stuff_1
  - const
  - callingconv
  - unity_64bit_support

colour: true
