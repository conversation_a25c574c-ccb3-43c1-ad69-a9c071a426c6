# ==============================================================================
#   CMock Project - Automatic Mock Generation for C
#   Copyright (c) 2007-2014 <PERSON>, <PERSON>, <PERSON>
#   [Released under MIT License. Please refer to license.txt for details]
# ==============================================================================

require '../config/test_environment'
require 'rake'
require 'rake/clean'
require 'rake/testtask'
require './rakefile_helper'

include RakefileHelpers

DEFAULT_CONFIG_FILE = 'gcc.yml'
CMOCK_TEST_ROOT = File.expand_path(File.dirname(__FILE__))

SYSTEM_TEST_SUPPORT_DIRS = [
  File.join(CMOCK_TEST_ROOT, 'system/generated'),
  File.join(CMOCK_TEST_ROOT, 'system/build')
]

SYSTEM_TEST_SUPPORT_DIRS.each do |dir|
  directory(dir)
  CLOBBER.include(dir)
end


task :prep_system_tests => SYSTEM_TEST_SUPPORT_DIRS

configure_clean
configure_toolchain(DEFAULT_CONFIG_FILE)

task :default => [:test]
task :ci      => [:no_color, :default, 'test:examples', 'style:check']
task :cruise  => :ci

desc "Load configuration"
task :config, :config_file do |t, args|
  args = {:config_file => DEFAULT_CONFIG_FILE} if args[:config_file].nil?
  args = {:config_file => args[:config_file] + '.yml'} unless args[:config_file] =~ /\.yml$/i
  configure_toolchain(args[:config_file])
end

# Still support testing everything with just 'test' but switch default to ceedling-like test:all
task :test => ['test:all']

namespace :test do
  desc "Run all unit, c, and system tests"
  task :all => [:clobber, :prep_system_tests, 'test:units', 'test:c', 'test:system']

  desc "Run Unit Tests"
  Rake::TestTask.new('units') do |t|
    t.pattern = 'unit/*_test.rb'
    t.verbose = true
  end

  #individual unit tests
  FileList['unit/*_test.rb'].each do |test|
    Rake::TestTask.new(File.basename(test,'.*').sub('_test','')) do |t|
      t.pattern = test
      t.verbose = true
    end
  end

  desc "Run C Unit Tests"
  task :c => [:prep_system_tests] do
    unless ($cfg['unsupported'].include? "C")
      build_and_test_c_files
    end
  end

  desc "Run System Tests"
  task :system => [:clobber, :prep_system_tests] do
    #get a list of all system tests, removing unsupported tests for this compiler
    sys_unsupported  = $cfg['unsupported'].map {|a| 'system/test_interactions/'+a+'.yml'}
    sys_tests_to_run = FileList['system/test_interactions/*.yml'] - sys_unsupported
    compile_unsupported  = $cfg['unsupported'].map {|a| SYSTEST_COMPILE_MOCKABLES_PATH+a+'.h'}
    compile_tests_to_run = FileList[SYSTEST_COMPILE_MOCKABLES_PATH + '*.h'] - compile_unsupported
    unless (sys_unsupported.empty? and compile_unsupported.empty?)
      report "\nIgnoring these system tests..."
      sys_unsupported.each {|a| report a}
      compile_unsupported.each {|a| report a}
    end
    report "\nRunning system tests..."
    tests_failed = run_system_test_interactions(sys_tests_to_run)
    raise "System tests failed." if (tests_failed > 0)

    run_system_test_compilations(compile_tests_to_run)
  end

  desc "Test cmock examples"
  task :examples => [:prep_system_tests] do
    run_examples()
  end

  #individual system tests
  FileList['system/test_interactions/*.yml'].each do |test|
    basename = File.basename(test,'.*')
    #desc "Run system test #{basename}"
    task basename do
      run_system_test_interactions([test])
    end
  end

  desc "Profile Mock Generation"
  task :profile => [:clobber, :prep_system_tests] do
    run_system_test_profiles(FileList[SYSTEST_COMPILE_MOCKABLES_PATH + '*.h'])
  end
end

task :no_color do
  $colour_output = false
end

################### CODING STYLE VALIDATION
namespace :style do
  desc "Check style"
  task :check do
    report "\nVERIFYING RUBY STYLE"
    report execute("rubocop ../lib ../examples ../config ../scripts --config ../vendor/unity/test/.rubocop.yml", true)
    report "Styling Ruby:PASS"
  end

  desc "Fix Style of all C Code"
  task :c do
    run_astyle("../src/*.* ../extras/fixture/src/*.*")
  end

  desc "Attempt to Autocorrect style"
  task :auto  => ['style:clean'] do
    report execute("rubocop ../lib ../examples ../config ../scripts --auto-correct --config ../vendor/unity/test/.rubocop.yml", true)
    report "Autocorrected What We Could."
  end

  desc "Update style todo list"
  task :todo  => ['style:clean'] do
    report execute("rubocop ../lib ../examples ../config ../scripts --auto-gen-config --config ../vendor/unity/test/.rubocop.yml", true)
    report "Updated Style TODO List."
  end

  task :clean do
    File.delete(".rubocop_todo.yml") if File.exists?(".rubocop_todo.yml")
  end
end

task :style => ['style:check']