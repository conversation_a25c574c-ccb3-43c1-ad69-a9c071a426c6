/*******************************************************************************
 *
 * Copyright (c) 2022 <PERSON> (TZI) and others.
 * All rights reserved. This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License v1.0
 * and Eclipse Distribution License v. 1.0 which accompanies this distribution.
 *
 * The Eclipse Public License is available at http://www.eclipse.org/legal/epl-v10.html
 * and the Eclipse Distribution License is available at
 * http://www.eclipse.org/org/documents/edl-v10.php.
 */

#include <assert.h>

#include "dtls_config.h"
#include "dtls_prng.h"
#include "test_prf.h"

#include "tinydtls.h"
#include "crypto.h"

#include <stdio.h>

/* Check against SHA256 test vector from
 * https://mailarchive.ietf.org/arch/msg/tls/fzVCzk-z3FShgGJ6DOXqM1ydxms/
 */
static void
t_test_prf0(void) {
  const uint8_t key[] = {
    0x9b, 0xbe, 0x43, 0x6b, 0xa9, 0x40, 0xf0, 0x17,
    0xb1, 0x76, 0x52, 0x84, 0x9a, 0x71, 0xdb, 0x35
  };
  const uint8_t label[] = {
    0x74, 0x65, 0x73, 0x74, 0x20, 0x6c, 0x61, 0x62, 0x65, 0x6c
  };
  const uint8_t random1[] = {
    0xa0, 0xba, 0x9f, 0x93, 0x6c, 0xda, 0x31, 0x18,
    0x27, 0xa6, 0xf7, 0x96, 0xff, 0xd5, 0x19, 0x8c
  };
  /* expected result */
  const uint8_t result[] = {
    0xe3, 0xf2, 0x29, 0xba, 0x72, 0x7b, 0xe1, 0x7b,
    0x8d, 0x12, 0x26, 0x20, 0x55, 0x7c, 0xd4, 0x53,
    0xc2, 0xaa, 0xb2, 0x1d, 0x07, 0xc3, 0xd4, 0x95,
    0x32, 0x9b, 0x52, 0xd4, 0xe6, 0x1e, 0xdb, 0x5a,
    0x6b, 0x30, 0x17, 0x91, 0xe9, 0x0d, 0x35, 0xc9,
    0xc9, 0xa4, 0x6b, 0x4e, 0x14, 0xba, 0xf9, 0xaf,
    0x0f, 0xa0, 0x22, 0xf7, 0x07, 0x7d, 0xef, 0x17,
    0xab, 0xfd, 0x37, 0x97, 0xc0, 0x56, 0x4b, 0xab,
    0x4f, 0xbc, 0x91, 0x66, 0x6e, 0x9d, 0xef, 0x9b,
    0x97, 0xfc, 0xe3, 0x4f, 0x79, 0x67, 0x89, 0xba,
    0xa4, 0x80, 0x82, 0xd1, 0x22, 0xee, 0x42, 0xc5,
    0xa7, 0x2e, 0x5a, 0x51, 0x10, 0xff, 0xf7, 0x01,
    0x87, 0x34, 0x7b, 0x66
  };
  uint8_t outbuf[sizeof(result)];
  size_t bytes_written;

  bytes_written = dtls_p_hash(HASH_SHA256,
                              key, sizeof(key),
                              label, sizeof(label),
                              random1, sizeof(random1),
                              NULL, 0, /* random2 */
                              outbuf, sizeof(outbuf));

  CU_ASSERT_EQUAL(bytes_written, sizeof(outbuf));
  CU_ASSERT(memcmp(outbuf, result, bytes_written) == 0);

  const size_t random2_len = sizeof(random1)/2;
  const uint8_t *random2 = random1 + sizeof(random1) - random2_len;

  bytes_written = dtls_p_hash(HASH_SHA256,
                              key, sizeof(key),
                              label, sizeof(label),
                              random1, sizeof(random1) - random2_len,
                              random2, random2_len,
                              outbuf, sizeof(outbuf));

  CU_ASSERT_EQUAL(bytes_written, sizeof(outbuf));
  CU_ASSERT(memcmp(outbuf, result, bytes_written) == 0);

}

static void
t_test_prf1(void) {
  const uint8_t key[] = { 0x73, 0x65, 0x63, 0x72, 0x65, 0x74 };
  const uint8_t label[] = {
    0x21, 0x3f, 0x20, 0x73, 0x6f, 0x6d, 0x65, 0x20, 0x6c,
    0x61, 0x62, 0x65, 0x6c, 0x2c, 0x20, 0x31, 0x32, 0x33
  };
  const uint8_t random[] = {
    0x72, 0x61, 0x6e, 0x64, 0x6f, 0x6d
  };
  /* expected result */
  const uint8_t result[] = {
    0xd0, 0x2d, 0xf7, 0x99, 0x9a, 0xe4, 0xed, 0x36, 0xb1,
    0xba, 0x07, 0x81, 0x9e, 0x5d, 0xa8, 0xb0, 0xbe, 0x13,
    0x19, 0xe7, 0x90, 0x46, 0xe5, 0xea, 0x05, 0xa3, 0xec,
    0xff, 0xfc, 0xd9, 0x59, 0xa4, 0x1a, 0x31, 0x48, 0xda,
    0xe3, 0x84, 0x1f, 0x81, 0x69, 0xd8, 0x01, 0xd9, 0x44,
    0x1c, 0xd9, 0xed, 0x83, 0xf7, 0x8a, 0x4f, 0xa3, 0x5c,
    0x4a, 0x76, 0x4d, 0x48, 0x90, 0x32
  };
  uint8_t outbuf[sizeof(result)];
  size_t bytes_written;

  bytes_written = dtls_p_hash(HASH_SHA256,
                              key, sizeof(key),
                              label, sizeof(label),
                              random, sizeof(random),
                              NULL, 0, /* random2 */
                              outbuf, sizeof(outbuf));

  CU_ASSERT_EQUAL(bytes_written, sizeof(outbuf));
  CU_ASSERT(memcmp(outbuf, result, bytes_written) == 0);
}

static void
t_test_prf2(void) {
  const uint8_t key[] = { 0x00 };
  const uint8_t random[] = {
    0x75, 0xfc, 0x29, 0x97, 0x64, 0x38, 0x2e, 0x1b, 0x91,
    0xe0, 0x28, 0x7d, 0x6a, 0xac, 0x66, 0x98, 0x25, 0x3f,
    0x08, 0xf4, 0x06, 0x66, 0x32, 0x18, 0x9f, 0x37, 0xb9,
    0xef, 0xd1, 0x01, 0xac, 0xe2, 0x0a, 0x3b, 0xda, 0x57,
    0x9c, 0x77, 0xc7, 0xa2, 0x86, 0x4d, 0x62, 0xaa, 0xf3,
    0xf1, 0xfc, 0xd0, 0x15, 0x62, 0xc4, 0x93, 0x6e, 0x0b,
    0x09, 0x03, 0xab, 0xc2, 0x8f, 0x0a, 0xbb, 0x5f, 0xbb,
    0x89, 0x45, 0xc3, 0x50, 0xb5, 0x6e, 0x65, 0xd5, 0x80,
    0x5d, 0xb8, 0x5e, 0xb2, 0x30, 0x34, 0xee, 0x17
  };
  /* expected result */
  const uint8_t result[] = {
    0x4c, 0xd2, 0xef, 0xad, 0x3e, 0x92, 0x28, 0xee, 0xb1,
    0xb2, 0x46, 0xbb, 0x3a, 0x80, 0xf0, 0x19, 0xde, 0x4e,
    0xfd, 0xbc, 0x8b, 0x2a, 0x36, 0xf4, 0xb1, 0x97, 0x5f,
    0xc9, 0x94, 0xb0, 0xc9, 0x68, 0x4f, 0xfa, 0x4f, 0x09,
    0x36, 0x9a, 0x89, 0x8f, 0xa4, 0xc1, 0xd5, 0x5c, 0x48,
    0x8a, 0xd2, 0xf9, 0x95, 0x23, 0xf4, 0x2a, 0x98, 0x54,
    0xa2, 0xb4, 0x68, 0xab, 0x52, 0xb7, 0x69, 0x11, 0x2e,
    0xfb, 0x81, 0x07, 0xf8, 0xb0, 0x78, 0x6f, 0x53, 0x18,
    0x60, 0x0c, 0xc0, 0xa3, 0x0d, 0x22, 0x8a, 0xa0, 0x90,
    0x8c, 0x40, 0x3f, 0x4f, 0xab, 0x95, 0x3d, 0xa5, 0x31,
    0x21, 0x8a, 0xc3, 0x4b, 0xa9, 0x8b, 0xba, 0x12, 0xd0
  };
  uint8_t outbuf[sizeof(result)];
  size_t bytes_written;

  bytes_written = dtls_p_hash(HASH_SHA256,
                              key, sizeof(key),
                              NULL, 0,
                              random, sizeof(random),
                              NULL, 0, /* random2 */
                              outbuf, sizeof(outbuf));

  CU_ASSERT_EQUAL(bytes_written, sizeof(outbuf));
  CU_ASSERT(memcmp(outbuf, result, bytes_written) == 0);
}

static void
t_test_prf3(void) {
  const uint8_t key[] = { 0x00, 0x01, 0x02, 0x03 };
  const uint8_t label[] = { 0x6c, 0x61, 0x62, 0x65, 0x6c };
  const uint8_t random1[] = {
    0x75, 0xfc, 0x29, 0x97, 0x64, 0x38, 0x2e, 0x1b,
    0x91, 0xe0, 0x28, 0x7d, 0x6a, 0xac, 0x66, 0x98,
    0x25, 0x3f, 0x08, 0xf4, 0x06, 0x66, 0x32, 0x18,
    0x9f, 0x37, 0xb9, 0xef, 0xd1, 0x01, 0xac, 0xe2,
  };
  const uint8_t random2[] = {
    0x0a, 0x3b, 0xda, 0x57, 0x9c, 0x77, 0xc7, 0xa2,
    0x86, 0x4d, 0x62, 0xaa, 0xf3, 0xf1, 0xfc, 0xd0,
    0x15, 0x62, 0xc4, 0x93, 0x6e, 0x0b, 0x09, 0x03,
    0xab, 0xc2, 0x8f, 0x0a, 0xbb, 0x5f, 0xbb, 0x89,
    0x45, 0xc3, 0x50, 0xb5, 0x6e, 0x65, 0xd5, 0x80,
    0x5d, 0xb8, 0x5e, 0xb2, 0x30, 0x34, 0xee, 0x17
  };
  /* expected result */
  const uint8_t result[] = { 0xf1, 0xb0, 0x85 };
  uint8_t outbuf[sizeof(result)];
  size_t bytes_written;

  bytes_written = dtls_p_hash(HASH_SHA256,
                              key, sizeof(key),
                              label, sizeof(label),
                              random1, sizeof(random1),
                              random2, sizeof(random2),
                              outbuf, sizeof(outbuf));

  CU_ASSERT_EQUAL(bytes_written, sizeof(outbuf));
  CU_ASSERT(memcmp(outbuf, result, bytes_written) == 0);
}

static void
t_test_prf4(void) {
  const uint8_t label[] = { 0x6c, 0x61, 0x62, 0x65, 0x6c };
  const uint8_t random1[] = {
    0x75, 0xfc, 0x29, 0x97, 0x64, 0x38, 0x2e, 0x1b,
    0x91, 0xe0, 0x28, 0x7d, 0x6a, 0xac, 0x66, 0x98,
    0x25, 0x3f, 0x08, 0xf4, 0x06, 0x66, 0x32, 0x18,
    0x9f, 0x37, 0xb9, 0xef, 0xd1, 0x01, 0xac, 0xe2,
  };
  const uint8_t random2[] = {
    0x0a, 0x3b, 0xda, 0x57, 0x9c, 0x77, 0xc7, 0xa2,
    0x86, 0x4d, 0x62, 0xaa, 0xf3, 0xf1, 0xfc, 0xd0,
    0x15, 0x62, 0xc4, 0x93, 0x6e, 0x0b, 0x09, 0x03,
    0xab, 0xc2, 0x8f, 0x0a, 0xbb, 0x5f, 0xbb, 0x89,
    0x45, 0xc3, 0x50, 0xb5, 0x6e, 0x65, 0xd5, 0x80,
    0x5d, 0xb8, 0x5e, 0xb2, 0x30, 0x34, 0xee, 0x17
  };
  /* expected result */
  const uint8_t result[] = {
    0xdc, 0xe1, 0x0a, 0x7d, 0xa0, 0xa8, 0x7c, 0x8c, 0xc6,
    0x1e, 0x33, 0xf6, 0xa9, 0x61, 0x96, 0x7a, 0x1c, 0x22,
    0xb6, 0xbc, 0x62, 0xe4, 0xb0, 0x19, 0x5b, 0x98, 0x2e,
    0xb5, 0xd8, 0x2a, 0xa2, 0x2e, 0x9c, 0xce, 0x79
  };
  uint8_t outbuf[sizeof(result)];
  size_t bytes_written;

  bytes_written = dtls_p_hash(HASH_SHA256,
                              NULL, 0,
                              label, sizeof(label),
                              random1, sizeof(random1),
                              random2, sizeof(random2),
                              outbuf, sizeof(outbuf));

  CU_ASSERT_EQUAL(bytes_written, sizeof(outbuf));
  CU_ASSERT(memcmp(outbuf, result, bytes_written) == 0);
}

static void
t_test_prf5(void) {
  /* expected result */
  const uint8_t result[] = {
    0xa6, 0xf9, 0x91, 0xab, 0x2b, 0x5f, 0x9e, 0xe8,
    0x06, 0x3c, 0x11, 0x5a, 0xb7, 0xb4, 0x00, 0x9b,
    0x0f, 0x3b, 0xcb, 0x8f, 0x10, 0x90, 0xe1, 0xaf,
    0xc4, 0xa3, 0xff, 0x20, 0x2e, 0x82, 0xed, 0x00
  };
  uint8_t outbuf[sizeof(result)];
  size_t bytes_written;

  bytes_written = dtls_p_hash(HASH_SHA256,
                              NULL, 0,  /* secret */
                              NULL, 0,  /* label */
                              NULL, 0,  /* random1 */
                              NULL, 0,  /* random2 */
                              outbuf, sizeof(outbuf));

  CU_ASSERT_EQUAL(bytes_written, sizeof(outbuf));
  CU_ASSERT(memcmp(outbuf, result, bytes_written) == 0);
}

CU_pSuite
t_init_prf_tests(void) {
  CU_pSuite suite;

  suite = CU_add_suite("PRF", NULL, NULL);
  if (!suite) {                        /* signal error */
    fprintf(stderr, "W: cannot add PRF test suite (%s)\n",
            CU_get_error_msg());

    return NULL;
  }

  /* On POSIX system, getrandom() is used where available, ignoring
   * the seed. For other platforms, the random sequence will be the
   * same for each run. */
  dtls_prng_init(0);

#define PRF_TEST(s,t)                                                   \
  if (!CU_ADD_TEST(s,t)) {                                              \
    fprintf(stderr, "W: cannot add test for PRF (%s)\n",                \
            CU_get_error_msg());                                        \
  }

  PRF_TEST(suite, t_test_prf0);
  PRF_TEST(suite, t_test_prf1);
  PRF_TEST(suite, t_test_prf2);
  PRF_TEST(suite, t_test_prf3);
  PRF_TEST(suite, t_test_prf4);
  PRF_TEST(suite, t_test_prf5);

  return suite;
}

