/*
 * coap_config.h.riot -- RIOT configuration for libcoap
 *
 * Copyright (C) 2021-2024 <PERSON> <berg<PERSON>@tzi.org> and others
 *
 * SPDX-License-Identifier: BSD-2-Clause
 *
 * This file is part of the CoAP library libcoap. Please see README for terms
 * of use.
 */

#ifndef COAP_CONFIG_H_
#define COAP_CONFIG_H_

#define WITH_POSIX 1

#ifndef COAP_CONSTRAINED_STACK
#define COAP_CONSTRAINED_STACK 1
#endif

/*
 * For systems that define int as being 2 bytes, need to make sure the
 * minimum configuration is set up (overridden if explicitely defined).
 */
#include <limits.h>

#if (UINT_MAX <= 65535)
#define COAP_DISABLE_TCP 1
#define COAP_AF_UNIX_SUPPORT 0
#define COAP_OSCORE_SUPPORT 0
#define COAP_WITH_OBSERVE_PERSIST 0
#define COAP_WS_SUPPORT 0
#define COAP_Q_BLOCK_SUPPORT 0
#define COAP_MAX_LOGGING_LEVEL 0
#endif /* UINT_MAX < 65536 */

#ifdef CONFIG_LIBCOAP_MAX_LOGGING_LEVEL
#ifndef COAP_MAX_LOGGING_LEVEL
/* Define 0-8 for max logging levels. */
#define COAP_MAX_LOGGING_LEVEL CONFIG_LIBCOAP_MAX_LOGGING_LEVEL
#endif /* COAP_MAX_LOGGING_LEVEL */
#else /* ! CONFIG_LIBCOAP_MAX_LOGGING_LEVEL */
#ifndef COAP_MAX_LOGGING_LEVEL
/* Define 0-8 for max logging levels. */
#define COAP_MAX_LOGGING_LEVEL 4
#endif /* COAP_MAX_LOGGING_LEVEL */
#endif /* ! CONFIG_LIBCOAP_MAX_LOGGING_LEVEL */

#ifdef CONFIG_LIBCOAP_IPV4_SUPPORT
#ifndef COAP_IPV4_SUPPORT
/* Define to 1 to build with IPv4 support. */
#define COAP_IPV4_SUPPORT 1
#endif /* COAP_IPV4_SUPPORT */
#endif /* CONFIG_LIBCOAP_IPV4_SUPPORT */

#ifdef CONFIG_LIBCOAP_IPV6_SUPPORT
#ifndef COAP_IPV6_SUPPORT
/* Define to 1 to build with IPv6 support. */
#define COAP_IPV6_SUPPORT 1
#endif /* COAP_IPV6_SUPPORT */
#endif /* CONFIG_LIBCOAP_IPV6_SUPPORT */

#ifdef CONFIG_LIBCOAP_AF_UNIX_SUPPORT
#ifndef COAP_AF_UNIX_SUPPORT
/* Define to 1 to build with Unix socket support. */
#define COAP_AF_UNIX_SUPPORT 1
#endif /* COAP_AF_UNIX_SUPPORT */
#endif /* CONFIG_LIBCOAP_AF_UNIX_SUPPORT */

#ifdef CONFIG_LIBCOAP_TCP_SUPPORT
#ifndef COAP_DISABLE_TCP
/* Define to 1 to build without TCP support. */
#define COAP_DISABLE_TCP 0
#endif /* COAP_DISABLE_TCP */
#else /* ! CONFIG_LIBCOAP_TCP_SUPPORT */
#ifndef COAP_DISABLE_TCP
/* Define to 1 to build without TCP support. */
#define COAP_DISABLE_TCP 1
#endif /* ! COAP_DISABLE_TCP */
#endif /* ! CONFIG_LIBCOAP_TCP_SUPPORT */

#ifdef CONFIG_LIBCOAP_OSCORE_SUPPORT
#ifndef COAP_OSCORE_SUPPORT
/* Define to 1 if the library has OSCORE support. */
#define COAP_OSCORE_SUPPORT 1
#endif /* COAP_OSCORE_SUPPORT */
#endif /* CONFIG_LIBCOAP_OSCORE_SUPPORT */

#ifdef CONFIG_LIBCOAP_WITH_OBSERVE_PERSIST
#ifndef COAP_WITH_OBSERVE_PERSIST
/* Define to 1 if the library has Observe persist support. */
#define COAP_WITH_OBSERVE_PERSIST 0
#endif /* COAP_WITH_OBSERVE_PERSIST */
#endif /* CONFIG_LIBCOAP_WITH_OBSERVE_PERSIST */

#ifdef CONFIG_LIBCOAP_WS_SUPPORT
#ifndef COAP_WS_SUPPORT
/* Define to 1 if the library has WebSockets support. */
#define COAP_WS_SUPPORT 0
#endif /* COAP_WS_SUPPORT */
#endif /* CONFIG_LIBCOAP_WS_SUPPORT */

#ifdef CONFIG_LIBCOAP_Q_BLOCK_SUPPORT
#ifndef COAP_Q_BLOCK_SUPPORT
/* Define to 1 to build with Q-Block (RFC9177) support. */
#define COAP_Q_BLOCK_SUPPORT 0
#endif /* COAP_Q_BLOCK_SUPPORT */
#endif /* CONFIG_LIBCOAP_Q_BLOCK_SUPPORT */

#ifdef CONFIG_LIBCOAP_CLIENT_SUPPORT
#ifndef COAP_CLIENT_SUPPORT
/* Define to 1 if the library has client support. */
#define COAP_CLIENT_SUPPORT 1
#endif /* COAP_CLIENT_SUPPORT */
#endif /* CONFIG_LIBCOAP_CLIENT_SUPPORT */

#ifdef CONFIG_LIBCOAP_SERVER_SUPPORT
#ifndef COAP_SERVER_SUPPORT
/* Define to 1 if the library has server support. */
#define COAP_SERVER_SUPPORT 1
#endif /* COAP_SERVER_SUPPORT */
#endif /* CONFIG_LIBCOAP_SERVER_SUPPORT */

#ifdef CONFIG_LIBCOAP_PROXY_SUPPORT
#if COAP_CLIENT_SUPPORT && COAP_SERVER_SUPPORT
#ifndef COAP_PROXY_SUPPORT
/* Define to 1 if the library has proxy code support. */
#define COAP_PROXY_SUPPORT 1
#endif /* COAP_CLIENT_SUPPORT && COAP_SERVER_SUPPORT */
#endif /* COAP_PROXY_SUPPORT */
#endif /* CONFIG_LIBCOAP_PROXY_SUPPORT */

#ifdef CONFIG_LIBCOAP_ASYNC_SUPPORT
#ifndef COAP_ASYNC_SUPPORT
/* Define to 1 to build with support for async separate responses. */
#define COAP_ASYNC_SUPPORT 1
#endif /* COAP_ASYNC_SUPPORT */
#endif /* CONFIG_LIBCOAP_ASYNC_SUPPORT */

#ifdef CONFIG_LIBCOAP_THREAD_SAFE
#ifndef COAP_THREAD_SAFE
/* Define to 1 if libcoap has thread safe support. */
#define COAP_THREAD_SAFE 1
#endif /* COAP_THREAD_SAFE */
#endif /* CONFIG_LIBCOAP_THREAD_SAFE */

#ifdef CONFIG_LIBCOAP_THREAD_RECURSIVE_CHECK
#ifndef COAP_THREAD_RECURSIVE_CHECK
/* Define to 1 to build with thread recursive lock detection support. */
#define COAP_THREAD_RECURSIVE_CHECK 1
#endif /* COAP_THREAD_RECURSIVE_CHECK */
#endif /* CONFIG_LIBCOAP_THREAD_RECURSIVE_CHECK */

#ifdef CONFIG_LIBCOAP_MAX_STRING_SIZE
#ifndef COAP_MAX_STRING_SIZE
#define COAP_MAX_STRING_SIZE CONFIG_LIBCOAP_MAX_STRING_SIZE
#endif /* COAP_MAX_STRING_SIZE */
#endif /* CONFIG_LIBCOAP_OSCORE_SUPPORT */

#ifdef CONFIG_LIBCOAP_MAX_ENDPOINTS
#ifndef COAP_MAX_ENDPOINTS
#define COAP_MAX_ENDPOINTS CONFIG_LIBCOAP_MAX_ENDPOINTS
#endif /* COAP_MAX_ENDPOINTS */
#endif /* CONFIG_LIBCOAP_MAX_ENDPOINTS */

#ifdef CONFIG_LIBCOAP_MAX_RESOURCES
#ifndef COAP_MAX_RESOURCES
#define COAP_MAX_RESOURCES CONFIG_LIBCOAP_MAX_RESOURCES
#endif /* COAP_MAX_RESOURCES */
#endif /* CONFIG_LIBCOAP_MAX_RESOURCES */

#ifdef CONFIG_LIBCOAP_MAX_ATTRIBUTE_SIZE
#ifndef COAP_MAX_ATTRIBUTE_SIZE
#define COAP_MAX_ATTRIBUTE_SIZE CONFIG_LIBCOAP_MAX_ATTRIBUTE_SIZE
#endif /* COAP_MAX_ATTRIBUTE_SIZE */
#endif /* CONFIG_LIBCOAP_MAX_ATTRIBUTE_SIZE */

#ifdef CONFIG_LIBCOAP_MAX_ATTRIBUTES
#ifndef COAP_MAX_ATTRIBUTES
#define COAP_MAX_ATTRIBUTES CONFIG_LIBCOAP_MAX_ATTRIBUTES
#endif /* COAP_MAX_ATTRIBUTES */
#endif /* CONFIG_LIBCOAP_MAX_ATTRIBUTES */

#ifdef CONFIG_LIBCOAP_MAX_PACKETS
#ifndef COAP_MAX_PACKETS
#define COAP_MAX_PACKETS CONFIG_LIBCOAP_MAX_PACKETS
#endif /* COAP_MAX_PACKETS */
#endif /* CONFIG_LIBCOAP_MAX_PACKETS */

#ifdef CONFIG_LIBCOAP_MAX_NODES
#ifndef COAP_MAX_NODES
#define COAP_MAX_NODES CONFIG_LIBCOAP_MAX_NODES
#endif /* COAP_MAX_NODES */
#endif /* CONFIG_LIBCOAP_MAX_NODES */

#ifdef CONFIG_LIBCOAP_MAX_CONTEXTS
#ifndef COAP_MAX_CONTEXTS
#define COAP_MAX_CONTEXTS CONFIG_LIBCOAP_MAX_CONTEXTS
#endif /* COAP_MAX_CONTEXTS */
#endif /* CONFIG_LIBCOAP_MAX_CONTEXTS */

#ifdef CONFIG_LIBCOAP_MAX_PDUS
#ifndef COAP_MAX_PDUS
#define COAP_MAX_PDUS CONFIG_LIBCOAP_MAX_PDUS
#endif /* COAP_MAX_PDUS */
#endif /* CONFIG_LIBCOAP_MAX_PDUS */

#ifdef CONFIG_LIBCOAP_MAX_DTLS_SESSIONS
#ifndef COAP_MAX_DTLS_SESSIONS
#define COAP_MAX_DTLS_SESSIONS CONFIG_LIBCOAP_MAX_DTLS_SESSIONS
#endif /* COAP_MAX_DTLS_SESSIONS */
#endif /* CONFIG_LIBCOAP_MAX_DTLS_SESSIONS */

#ifdef CONFIG_LIBCOAP_MAX_SESSIONS
#ifndef COAP_MAX_SESSIONS
#define COAP_MAX_SESSIONS CONFIG_LIBCOAP_MAX_SESSIONS
#endif /* COAP_MAX_SESSIONS */
#endif /* CONFIG_LIBCOAP_MAX_SESSIONS */

#ifdef CONFIG_LIBCOAP_MAX_OPTION_SIZE
#ifndef COAP_MAX_OPTION_SIZE
#define COAP_MAX_OPTION_SIZE CONFIG_LIBCOAP_MAX_OPTION_SIZE
#endif /* COAP_MAX_OPTION_SIZE */
#endif /* CONFIG_LIBCOAP_MAX_OPTION_SIZE */

#ifdef CONFIG_LIBCOAP_MAX_OPTIONS
#ifndef COAP_MAX_OPTIONS
#define COAP_MAX_OPTIONS CONFIG_LIBCOAP_MAX_OPTIONS
#endif /* COAP_MAX_OPTIONS */
#endif /* CONFIG_LIBCOAP_MAX_OPTIONS */

#ifdef CONFIG_LIBCOAP_MAX_CACHE_KEYS
#ifndef COAP_MAX_CACHE_KEYS
#define COAP_MAX_CACHE_KEYS CONFIG_LIBCOAP_MAX_CACHE_KEYS
#endif /* COAP_MAX_CACHE_KEYS */
#endif /* CONFIG_LIBCOAP_MAX_CACHE_KEYS */

#ifdef CONFIG_LIBCOAP_MAX_CACHE_ENTRIES
#ifndef COAP_MAX_CACHE_ENTRIES
#define COAP_MAX_CACHE_ENTRIES CONFIG_LIBCOAP_MAX_CACHE_ENTRIES
#endif /* COAP_MAX_CACHE_ENTRIES */
#endif /* CONFIG_LIBCOAP_MAX_CACHE_ENTRIES */

#ifdef CONFIG_LIBCOAP_MAX_LG_CRCVS
#ifndef COAP_MAX_LG_CRCVS
#define COAP_MAX_LG_CRCVS CONFIG_LIBCOAP_MAX_LG_CRCVS
#endif /* COAP_MAX_LG_CRCVS */
#endif /* CONFIG_LIBCOAP_MAX_LG_CRCVS */

#ifdef CONFIG_LIBCOAP_MAX_LG_SRCVS
#ifndef COAP_MAX_LG_SRCVS
#define COAP_MAX_LG_SRCVS CONFIG_LIBCOAP_MAX_LG_SRCVS
#endif /* COAP_MAX_LG_SRCVS */
#endif /* CONFIG_LIBCOAP_MAX_LG_SRCVS */

#ifdef CONFIG_LIBCOAP_MAX_LG_XMITS
#ifndef COAP_MAX_LG_XMITS
#define COAP_MAX_LG_XMITS CONFIG_LIBCOAP_MAX_LG_XMITS
#endif /* COAP_MAX_LG_XMITS */
#endif /* CONFIG_LIBCOAP_MAX_LG_XMITS */

#ifndef COAP_DISABLE_TCP
#if MODULE_SOCK_TCP
#define COAP_DISABLE_TCP 0
#else /* ! MODULE_SOCK_TCP */
#define COAP_DISABLE_TCP 1
#endif /* ! MODULE_SOCK_TCP */
#endif /* COAP_DISABLE_TCP */

#ifdef MODULE_TINYDTLS
#ifndef COAP_WITH_LIBTINYDTLS
#define COAP_WITH_LIBTINYDTLS 1
#endif /* ! COAP_WITH_LIBTINYDTLS */
#endif /* MODULE_TINYDTLS */

/* Define if building universal (internal helper macro) */
/* #undef AC_APPLE_UNIVERSAL_BUILD */

/* Define to 1 if you have the <arpa/inet.h> header file. */
/* #undef HAVE_ARPA_INET_H */

/* Define to 1 if you have the <assert.h> header file. */
#define HAVE_ASSERT_H 1

/* Define to 1 if you have the `getaddrinfo' function. */
/* #undef HAVE_GETADDRINFO */

/* Define to 1 if you have the <inttypes.h> header file. */
#define HAVE_INTTYPES_H 1

/* Define to 1 if you have the <limits.h> header file. */
/* #undef HAVE_LIMITS_H */

/* Define to 1 if your system has a GNU libc compatible `malloc' function, and
   to 0 otherwise. */
#undef HAVE_MALLOC

/* Define to 1 if you have the <memory.h> header file. */
/* #undef HAVE_MEMORY_H */

/* Define to 1 if you have the `memset' function. */
#define HAVE_MEMSET 1

/* Define to 1 if you have the <netdb.h> header file. */
/* #undef HAVE_NETDB_H */

/* Define to 1 if you have the <netinet/in.h> header file. */
/* #define HAVE_NETINET_IN_H */

/* Define to 1 if you have the `select' function. */
/* #undef HAVE_SELECT */

/* Define to 1 if you have the `socket' function. */
/* #undef HAVE_SOCKET */

/* Define to 1 if you have the <stdint.h> header file. */
#define HAVE_STDINT_H 1

/* Define to 1 if you have the <stdlib.h> header file. */
#define HAVE_STDLIB_H 1

/* Define to 1 if you have the `strcasecmp' function. */
#define HAVE_STRCASECMP 1

/* Define to 1 if you have the <strings.h> header file. */
#define HAVE_STRINGS_H 1

/* Define to 1 if you have the <string.h> header file. */
#define HAVE_STRING_H 1

/* Define to 1 if you have the `strnlen' function. */
#define HAVE_STRNLEN 1

/* Define to 1 if you have the `strrchr' function. */
#define HAVE_STRRCHR 1

/* Define to 1 if you have the <sys/socket.h> header file. */
/* #undef HAVE_SYS_SOCKET_H */

/* Define to 1 if you have the <sys/stat.h> header file. */
/* #undef HAVE_SYS_STAT_H */

/* Define to 1 if you have the <sys/time.h> header file. */
#define HAVE_SYS_TIME_H 1

/* Define to 1 if you have the <sys/types.h> header file. */
#define HAVE_SYS_TYPES_H 1

/* Define to 1 if you have the <sys/unistd.h> header file. */
#define HAVE_SYS_UNISTD_H 1

/* Define to 1 if you have the <time.h> header file. */
#define HAVE_TIME_H 1

/* Define to 1 if you have the <unistd.h> header file. */
#define HAVE_UNISTD_H 1

#ifndef PACKAGE_BUGREPORT
/* Define to the address where bug reports for this package should be sent. */
#define PACKAGE_BUGREPORT "<EMAIL>"
#endif /* PACKAGE_BUGREPORT */

#ifndef PACKAGE_NAME
/* Define to the full name of this package. */
#define PACKAGE_NAME "libcoap"
#endif /* PACKAGE_NAME */

#ifndef PACKAGE_STRING
/* Define to the full name and version of this package. */
#define PACKAGE_STRING "libcoap 4.3.5"
#endif /* PACKAGE_STRING */

#ifndef PACKAGE_TARNAME
/* Define to the one symbol short name of this package. */
#define PACKAGE_TARNAME "libcoap"
#endif /* PACKAGE_TARNAME */

#ifndef PACKAGE_URL
/* Define to the home page for this package. */
#define PACKAGE_URL "https://libcoap.net/"
#endif /* PACKAGE_URL */

#ifndef PACKAGE_VERSION
/* Define to the version of this package. */
#define PACKAGE_VERSION "4.3.5"
#endif /* PACKAGE_VERSION */

/* Define to 1 if you have the ANSI C header files. */
#define STDC_HEADERS 1

/* Define WORDS_BIGENDIAN to 1 if your processor stores words with the most
   significant byte first (like Motorola and SPARC, unlike Intel). */
#if defined AC_APPLE_UNIVERSAL_BUILD
# if defined __BIG_ENDIAN__
#  define WORDS_BIGENDIAN 1
# endif
#else
# ifndef WORDS_BIGENDIAN
/* #  undef WORDS_BIGENDIAN */
# endif
#endif

/* Define to rpl_malloc if the replacement function should be used. */
/* #undef malloc */

/* Define to `unsigned int' if <sys/types.h> does not define. */
/* #undef size_t */

/* Define to `int' if <sys/types.h> does not define. */
/* #undef ssize_t */

#endif /* COAP_CONFIG_H_ */
