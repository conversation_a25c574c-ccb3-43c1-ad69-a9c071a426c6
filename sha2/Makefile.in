# Makefile for tinydtls
#
# Copyright (c) 2011-2020 <PERSON> (TZI) and others.
# All rights reserved. This program and the accompanying materials
# are made available under the terms of the Eclipse Public License v1.0
# and Eclipse Distribution License v. 1.0 which accompanies this distribution.
#
# The Eclipse Public License is available at http://www.eclipse.org/legal/epl-v10.html
# and the Eclipse Distribution License is available at 
# http://www.eclipse.org/org/documents/edl-v10.php.
#
# Contributors:
#    <PERSON>  - initial API and implementation
#

# the library's version
VERSION:=@PACKAGE_VERSION@

# tools
@SET_MAKE@
SHELL = /bin/sh
MKDIR = mkdir
CC=@CC@

abs_builddir = @abs_builddir@
top_builddir = @top_builddir@
top_srcdir:= @top_srcdir@

SOURCES:= sha2.c
HEADERS:=sha2.h
OBJECTS:= $(patsubst %.c, %.o, $(SOURCES))
CPPFLAGS=@CPPFLAGS@ -I$(top_srcdir)
CFLAGS=-DSHA2_USE_INTTYPES_H -Wall -std=c99 -pedantic @CFLAGS@ @WARNING_CFLAGS@ $(EXTRA_CFLAGS)
LDFLAGS=@LDFLAGS@
LDLIBS=@LIBS@
FILES:=Makefile.in $(SOURCES) $(HEADERS) README sha2prog.c sha2speed.c sha2test.pl 
DISTDIR=$(top_builddir)/@PACKAGE_TARNAME@-@PACKAGE_VERSION@

.PHONY: all dirs clean install dist distclean .gitignore doc uninstall

.SUFFIXES:
.SUFFIXES:      .c .o

all:

check:	
	echo DISTDIR: $(DISTDIR)
	echo top_builddir: $(top_builddir)

clean:
	@rm -f $(PROGRAMS) main.o $(LIB) $(OBJECTS)
	for dir in $(SUBDIRS); do \
		$(MAKE) -C $$dir clean ; \
	done

distclean:	clean
	@rm -rf $(DISTDIR)
	@rm -f *~ $(DISTDIR).tar.gz

dist:	$(FILES)
	test -d $(DISTDIR)/sha2 || mkdir $(DISTDIR)/sha2
	cp -p $(FILES) $(DISTDIR)/sha2
	test -d $(DISTDIR)/sha2/testvectors || mkdir $(DISTDIR)/sha2/testvectors
	cp -pr testvectors $(DISTDIR)/sha2/testvectors

install:	$(HEADERS)
	test -d $(DESTDIR)$(includedir)/sha2 || mkdir -p $(DESTDIR)$(includedir)/sha2
	$(install) $(HEADERS) $(DESTDIR)$(includedir)/sha2

uninstall:
	for file in $(HEADERS); do \
		if test -f $(DESTDIR)$(includedir)/sha2/$$file ; then $(RM) $(DESTDIR)$(includedir)/sha2/$$file ; fi ;\
	done
	if test -d $(DESTDIR)$(includedir)/sha2 ; then $(RMDIR) $(DESTDIR)$(includedir)/sha2 ; fi

.gitignore:
	echo "core\n*~\n*.[oa]\n*.gz\n*.cap\n$(PROGRAM)\n$(DISTDIR)\n.gitignore" >$@
