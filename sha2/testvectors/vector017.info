DESCRIPTION:
    <PERSON><PERSON><PERSON> notified me that my implementation differed
    with several others on several test data sets he had tested
    against.  This test vector data set is one Rogier provided
    to me that highlighted an off-by-one bug in my implementation
    that affected SHA-256/384/512 hashes where the data set length
    was of a certain length.  In the case of SHA512 or SHA384, if
    the data length after subtracting 111 was an even multiple of
    128 bytes, the bug surfaced.  In the case of SHA256, after
    subtracting 55, the remaining length was an even multiple of 64,
    the bug surfaced.  The fix was simple.  In SHA512_Last() and in
    SHA256_Final() functions,  I simply replaced a single "<" test
    with a "<=" test.

    Thanks, Rogier!
   
    (Total length of test vector data: 12271)

FILE:
    vector017.dat

SHA256:
    88ee6ada861083094f4c64b373657e178d88ef0a4674fce6e4e1d84e3b176afb

SHA384:
    78cc6402a29eb984b8f8f888ab0102cabe7c06f0b9570e3d8d744c969db14397
    f58ecd14e70f324bf12d8dd4cd1ad3b2

SHA512:
    211bec83fbca249c53668802b857a9889428dc5120f34b3eac1603f13d1b4796
    5c387b39ef6af15b3a44c5e7b6bbb6c1096a677dc98fc8f472737540a332f378

