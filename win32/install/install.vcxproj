<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug DLL|Win32">
      <Configuration>Debug DLL</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug DLL|x64">
      <Configuration>Debug DLL</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release DLL|Win32">
      <Configuration>Release DLL</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release DLL|x64">
      <Configuration>Release DLL</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="NoTLS|x64">
      <Configuration>NoTLS</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>15.0</VCProjectVersion>
    <ProjectGuid>{150F429D-82C6-4EA2-B1B2-16EF35F9C11A}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>$(LatestTargetPlatformVersion)</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>$(DefaultPlatformToolset)</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug DLL|Win32'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>$(DefaultPlatformToolset)</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>$(DefaultPlatformToolset)</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release DLL|Win32'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>$(DefaultPlatformToolset)</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>$(DefaultPlatformToolset)</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug DLL|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>$(DefaultPlatformToolset)</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>$(DefaultPlatformToolset)</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release DLL|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>$(DefaultPlatformToolset)</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='NoTLS|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>$(DefaultPlatformToolset)</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\libcoap.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug DLL|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\libcoap.props" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\libcoap.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release DLL|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\libcoap.props" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\libcoap.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug DLL|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\libcoap.props" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\libcoap.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release DLL|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\libcoap.props" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='NoTLS|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\libcoap.props" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <NMakePreprocessorDefinitions>WIN32;_DEBUG;$(NMakePreprocessorDefinitions)</NMakePreprocessorDefinitions>
    <OutDir>$(InstallDirDbg)</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug DLL|Win32'">
    <NMakePreprocessorDefinitions>WIN32;_DEBUG;$(NMakePreprocessorDefinitions)</NMakePreprocessorDefinitions>
    <OutDir>$(InstallDirDbg)</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <NMakePreprocessorDefinitions>_DEBUG;$(NMakePreprocessorDefinitions)</NMakePreprocessorDefinitions>
    <OutDir>$(InstallDirDbg)</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug DLL|x64'">
    <NMakePreprocessorDefinitions>_DEBUG;$(NMakePreprocessorDefinitions)</NMakePreprocessorDefinitions>
    <OutDir>$(InstallDirDbg)</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <NMakePreprocessorDefinitions>WIN32;NDEBUG;$(NMakePreprocessorDefinitions)</NMakePreprocessorDefinitions>
    <OutDir>$(InstallDir)</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release DLL|Win32'">
    <NMakePreprocessorDefinitions>WIN32;NDEBUG;$(NMakePreprocessorDefinitions)</NMakePreprocessorDefinitions>
    <OutDir>$(InstallDir)</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <NMakePreprocessorDefinitions>NDEBUG;$(NMakePreprocessorDefinitions)</NMakePreprocessorDefinitions>
    <OutDir>$(InstallDir)</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release DLL|x64'">
    <NMakePreprocessorDefinitions>NDEBUG;$(NMakePreprocessorDefinitions)</NMakePreprocessorDefinitions>
    <OutDir>$(InstallDir)</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='NoTLS|x64'">
    <NMakePreprocessorDefinitions>NDEBUG;$(NMakePreprocessorDefinitions)</NMakePreprocessorDefinitions>
    <OutDir>$(InstallDir)</OutDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <PostBuildEvent>
      <Message>Installing</Message>
      <Command>if not exist "$(OutDir)$(LibCoAPIncludeDir)" mkdir "$(OutDir)$(LibCoAPIncludeDir)"
copy "$(SolutionDir)..\$(LibCoAPIncludeDir)\*.h" "$(OutDir)$(LibCoAPIncludeDir)"
if not exist "$(OutDir)bin" mkdir "$(OutDir)bin"
for %%f in ( coap-client$(DbgSuffix).exe coap-client$(DbgSuffix).pdb coap-server$(DbgSuffix).exe coap-server$(DbgSuffix).pdb coap-rd$(DbgSuffix).exe coap-rd$(DbgSuffix).pdb ) do copy "$(SolutionDir)$(Configuration)%%f" "$(OutDir)bin"
if not exist "$(OutDir)lib" mkdir "$(OutDir)lib"
copy "$(SolutionDir)$(Configuration)\libcoap$(DbgSuffix).lib" "$(OutDir)lib"
copy "$(SolutionDir)$(Configuration)\libcoap$(DbgSuffix).pdb" "$(OutDir)lib"</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug DLL|Win32'">
    <PostBuildEvent>
      <Message>Installing</Message>
      <Command>if not exist "$(OutDir)$(LibCoAPIncludeDir)" mkdir "$(OutDir)$(LibCoAPIncludeDir)"
copy "$(SolutionDir)..\$(LibCoAPIncludeDir)\*.h" "$(OutDir)$(LibCoAPIncludeDir)"
if not exist "$(OutDir)bin" mkdir "$(OutDir)bin"
for %%f in ( coap-client$(DbgSuffix).exe coap-client$(DbgSuffix).pdb coap-server$(DbgSuffix).exe coap-server$(DbgSuffix).pdb coap-rd$(DbgSuffix).exe coap-rd$(DbgSuffix).pdb libcoap-3$(DbgSuffix)-openssl.dll libcoap-3$(DbgSuffix)-openssl.pdb ) do copy "$(SolutionDir)$(Configuration)\%%f" "$(OutDir)bin"
if not exist "$(OutDir)lib" mkdir "$(OutDir)lib"
copy "$(SolutionDir)$(Configuration)\libcoap-3$(DbgSuffix)-openssl.lib" "$(OutDir)lib"
</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <PostBuildEvent>
      <Message>Installing</Message>
      <Command>if not exist "$(OutDir)$(LibCoAPIncludeDir)" mkdir "$(OutDir)$(LibCoAPIncludeDir)"
copy "$(SolutionDir)..\$(LibCoAPIncludeDir)\*.h" "$(OutDir)$(LibCoAPIncludeDir)"
if not exist "$(OutDir)bin" mkdir "$(OutDir)bin"
for %%f in ( coap-client.exe coap-client.pdb coap-server.exe coap-server.pdb coap-rd.exe coap-rd.pdb ) do copy "$(SolutionDir)$(Configuration)\%%f" "$(OutDir)bin"
if not exist "$(OutDir)lib" mkdir "$(OutDir)lib"
copy "$(SolutionDir)$(Configuration)\libcoap.lib" "$(OutDir)lib"
copy "$(SolutionDir)$(Configuration)\libcoap.pdb" "$(OutDir)lib"</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release DLL|Win32'">
    <PostBuildEvent>
      <Message>Installing</Message>
      <Command>if not exist "$(OutDir)$(LibCoAPIncludeDir)" mkdir "$(OutDir)$(LibCoAPIncludeDir)"
copy "$(SolutionDir)..\$(LibCoAPIncludeDir)\*.h" "$(OutDir)$(LibCoAPIncludeDir)"
if not exist "$(OutDir)bin" mkdir "$(OutDir)bin"
for %%f in ( coap-client.exe coap-client.pdb coap-server.exe coap-server.pdb coap-rd.exe coap-rd.pdb libcoap-3-openssl.dll libcoap-3-openssl.pdb ) do copy "$(SolutionDir)$(Configuration)\%%f" "$(OutDir)bin"
if not exist "$(OutDir)lib" mkdir "$(OutDir)lib"
copy "$(SolutionDir)$(Configuration)\libcoap-3-openssl.lib" "$(OutDir)lib"
</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <PostBuildEvent>
      <Command>if not exist "$(OutDir)$(LibCoAPIncludeDir)" mkdir "$(OutDir)$(LibCoAPIncludeDir)"
copy "$(SolutionDir)..\$(LibCoAPIncludeDir)\*.h" "$(OutDir)$(LibCoAPIncludeDir)"
if not exist "$(OutDir)bin" mkdir "$(OutDir)bin"
for %%f in ( coap-client$(DbgSuffix).exe coap-client$(DbgSuffix).pdb coap-server$(DbgSuffix).exe coap-server$(DbgSuffix).pdb coap-rd$(DbgSuffix).exe coap-rd$(DbgSuffix).pdb ) do copy "$(SolutionDir)$(Platform)\$(Configuration)\%%f" "$(OutDir)bin"
if not exist "$(OutDir)lib" mkdir "$(OutDir)lib"
copy "$(SolutionDir)$(Platform)\$(Configuration)\libcoap$(DbgSuffix).lib" "$(OutDir)lib"
copy "$(SolutionDir)$(Platform)\$(Configuration)\libcoap$(DbgSuffix).pdb" "$(OutDir)lib"</Command>
      <Message>Installing</Message>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug DLL|x64'">
    <PostBuildEvent>
      <Command>if not exist "$(OutDir)$(LibCoAPIncludeDir)" mkdir "$(OutDir)$(LibCoAPIncludeDir)"
copy "$(SolutionDir)..\$(LibCoAPIncludeDir)\*.h" "$(OutDir)$(LibCoAPIncludeDir)"
if not exist "$(OutDir)bin" mkdir "$(OutDir)bin"
for %%f in ( coap-client$(DbgSuffix).exe coap-client$(DbgSuffix).pdb coap-server$(DbgSuffix).exe coap-server$(DbgSuffix).pdb coap-rd$(DbgSuffix).exe coap-rd$(DbgSuffix).pdb libcoap-3$(DbgSuffix)-openssl.dll libcoap-3$(DbgSuffix)-openssl.pdb ) do copy "$(SolutionDir)$(Platform)\$(Configuration)\%%f" "$(OutDir)bin"
if not exist "$(OutDir)lib" mkdir "$(OutDir)lib"
copy "$(SolutionDir)$(Platform)\$(Configuration)\libcoap-3$(DbgSuffix)-openssl.lib" "$(OutDir)lib"
</Command>
      <Message>Installing</Message>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <PostBuildEvent>
      <Command>if not exist "$(OutDir)$(LibCoAPIncludeDir)" mkdir "$(OutDir)$(LibCoAPIncludeDir)"
copy "$(SolutionDir)..\$(LibCoAPIncludeDir)\*.h" "$(OutDir)$(LibCoAPIncludeDir)"
if not exist "$(OutDir)bin" mkdir "$(OutDir)bin"
for %%f in ( coap-client.exe coap-client.pdb coap-server.exe coap-server.pdb coap-rd.exe coap-rd.pdb ) do copy "$(SolutionDir)$(Platform)\$(Configuration)\%%f" "$(OutDir)bin"
if not exist "$(OutDir)lib" mkdir "$(OutDir)lib"
copy "$(SolutionDir)$(Platform)\$(Configuration)\libcoap.lib" "$(OutDir)lib"
copy "$(SolutionDir)$(Platform)\$(Configuration)\libcoap.pdb" "$(OutDir)lib"</Command>
      <Message>Installing</Message>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release DLL|x64'">
    <PostBuildEvent>
      <Command>if not exist "$(OutDir)$(LibCoAPIncludeDir)" mkdir "$(OutDir)$(LibCoAPIncludeDir)"
copy "$(SolutionDir)..\$(LibCoAPIncludeDir)\*.h" "$(OutDir)$(LibCoAPIncludeDir)"
if not exist "$(OutDir)bin" mkdir "$(OutDir)bin"
for %%f in ( coap-client.exe coap-client.pdb coap-server.exe coap-server.pdb coap-rd.exe coap-rd.pdb libcoap-3-openssl.dll libcoap-3-openssl.pdb ) do copy "$(SolutionDir)$(Platform)\$(Configuration)\%%f" "$(OutDir)bin"
if not exist "$(OutDir)lib" mkdir "$(OutDir)lib"
copy "$(SolutionDir)$(Platform)\$(Configuration)\libcoap-3-openssl.lib" "$(OutDir)lib"
</Command>
      <Message>Installing</Message>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='NoTLS|x64'">
    <PostBuildEvent>
      <Command>if not exist "$(OutDir)$(LibCoAPIncludeDir)" mkdir "$(OutDir)$(LibCoAPIncludeDir)"
copy "$(SolutionDir)..\$(LibCoAPIncludeDir)\*.h" "$(OutDir)$(LibCoAPIncludeDir)"
if not exist "$(OutDir)bin" mkdir "$(OutDir)bin"
for %%f in ( coap-client.exe coap-client.pdb coap-server.exe coap-server.pdb coap-rd.exe coap-rd.pdb ) do copy "$(SolutionDir)$(Platform)\$(Configuration)\%%f" "$(OutDir)bin"
if not exist "$(OutDir)lib" mkdir "$(OutDir)lib"
copy "$(SolutionDir)$(Platform)\$(Configuration)\libcoap-3.lib" "$(OutDir)lib"
copy "$(SolutionDir)$(Platform)\$(Configuration)\libcoap-3.pdb" "$(OutDir)lib"</Command>
      <Message>Installing</Message>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ProjectReference Include="..\coap-client\coap-client.vcxproj">
      <Project>{177f5822-f271-4903-9922-05adaf358b1e}</Project>
    </ProjectReference>
    <ProjectReference Include="..\coap-rd\coap-rd.vcxproj">
      <Project>{640ac988-fe9e-4580-bdf5-0d9fd57c3ce5}</Project>
    </ProjectReference>
    <ProjectReference Include="..\coap-server\coap-server.vcxproj">
      <Project>{f9255447-0c93-47cb-8644-62f0e6995791}</Project>
    </ProjectReference>
    <ProjectReference Include="..\libcoap.vcxproj">
      <Project>{96a98759-36b3-4246-a265-cafceec0f2f2}</Project>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
