<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug DLL|Win32">
      <Configuration>Debug DLL</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug DLL|x64">
      <Configuration>Debug DLL</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release DLL|Win32">
      <Configuration>Release DLL</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release DLL|x64">
      <Configuration>Release DLL</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="NoTLS|x64">
      <Configuration>NoTLS</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\src\coap_address.c" />
    <ClCompile Include="..\src\coap_async.c" />
    <ClCompile Include="..\src\coap_block.c" />
    <ClCompile Include="..\src\coap_cache.c" />
    <ClCompile Include="..\src\coap_debug.c" />
    <ClCompile Include="..\src\coap_dtls.c" />
    <ClCompile Include="..\src\coap_encode.c" />
    <ClCompile Include="..\src\coap_event.c" />
    <ClCompile Include="..\src\coap_hashkey.c" />
    <ClCompile Include="..\src\coap_gnutls.c" />
    <ClCompile Include="..\src\coap_io.c" />
    <ClCompile Include="..\src\coap_layers.c" />
    <ClCompile Include="..\src\coap_mbedtls.c" />
    <ClCompile Include="..\src\coap_mem.c" />
    <ClCompile Include="..\src\coap_net.c" />
    <ClCompile Include="..\src\coap_netif.c" />
    <ClCompile Include="..\src\coap_notls.c" />
    <ClCompile Include="..\src\coap_openssl.c" />
    <ClCompile Include="..\src\coap_option.c" />
    <ClCompile Include="..\src\coap_oscore.c" />
    <ClCompile Include="..\src\coap_pdu.c" />
    <ClCompile Include="..\src\coap_prng.c" />
    <ClCompile Include="..\src\coap_proxy.c" />
    <ClCompile Include="..\src\coap_resource.c" />
    <ClCompile Include="..\src\coap_session.c" />
    <ClCompile Include="..\src\coap_sha1.c" />
    <ClCompile Include="..\src\coap_str.c" />
    <ClCompile Include="..\src\coap_subscribe.c" />
    <ClCompile Include="..\src\coap_time.c" />
    <ClCompile Include="..\src\coap_tcp.c" />
    <ClCompile Include="..\src\coap_threadsafe.c" />
    <ClCompile Include="..\src\coap_tinydtls.c" />
    <ClCompile Include="..\src\coap_uri.c" />
    <ClCompile Include="..\src\coap_wolfssl.c" />
    <ClCompile Include="..\src\coap_ws.c" />
    <ClCompile Include="..\src\oscore\oscore.c" />
    <ClCompile Include="..\src\oscore\oscore_cbor.c" />
    <ClCompile Include="..\src\oscore\oscore_context.c" />
    <ClCompile Include="..\src\oscore\oscore_cose.c" />
    <ClCompile Include="..\src\oscore\oscore_crypto.c" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\coap_config.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\libcoap.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_address.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_asn1_internal.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_async.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_async_internal.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_block.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_block_internal.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_cache.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_cache_internal.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_crypto_internal.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_debug.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_debug_internal.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_dtls.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_dtls_internal.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_encode.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_event.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_forward_decls.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_hashkey_internal.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_internal.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_io.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_io_internal.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_layers_internal.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_mem.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_mutex_internal.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_net.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_net_internal.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_netif_internal.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_option.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_oscore.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_oscore_internal.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_pdu.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_pdu_internal.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_prng.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_prng_internal.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_proxy.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_resource.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_resource_internal.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_session.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_session_internal.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_sha1_internal.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_str.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_subscribe.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_subscribe_internal.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_supported.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_tcp_internal.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_threadsafe_internal.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_time.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_uri.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_uri_internal.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_uthash_internal.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_utlist_internal.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_ws.h" />
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_ws_internal.h" />
    <ClInclude Include="..\$(LibCoAPOSCOREIncludeDir)\oscore.h" />
    <ClInclude Include="..\$(LibCoAPOSCOREIncludeDir)\oscore_cbor.h" />
    <ClInclude Include="..\$(LibCoAPOSCOREIncludeDir)\oscore_context.h" />
    <ClInclude Include="..\$(LibCoAPOSCOREIncludeDir)\oscore_cose.h" />
    <ClInclude Include="..\$(LibCoAPOSCOREIncludeDir)\oscore_crypto.h" />
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="..\libcoap-3.sym">
      <FileType>Document</FileType>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='NoTLS|x64'">true</ExcludedFromBuild>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">ECHO LIBRARY $(TargetName) &gt; "$(IntDir)$(TargetName).def"
ECHO EXPORTS &gt;&gt;  "$(IntDir)$(TargetName).def"
TYPE %(FullPath) &gt;&gt; "$(IntDir)$(TargetName).def"</Command>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release DLL|Win32'">ECHO LIBRARY $(TargetName) &gt; "$(IntDir)$(TargetName).def"
ECHO EXPORTS &gt;&gt;  "$(IntDir)$(TargetName).def"
TYPE %(FullPath) &gt;&gt; "$(IntDir)$(TargetName).def"</Command>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug DLL|Win32'">ECHO LIBRARY $(TargetName) &gt; "$(IntDir)$(TargetName).def"
ECHO EXPORTS &gt;&gt;  "$(IntDir)$(TargetName).def"
TYPE %(FullPath) &gt;&gt; "$(IntDir)$(TargetName).def"</Command>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">ECHO LIBRARY $(TargetName) &gt; "$(IntDir)$(TargetName).def"
ECHO EXPORTS &gt;&gt;  "$(IntDir)$(TargetName).def"
TYPE %(FullPath) &gt;&gt; "$(IntDir)$(TargetName).def"</Command>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">ECHO LIBRARY $(TargetName) &gt; "$(IntDir)$(TargetName).def"
ECHO EXPORTS &gt;&gt;  "$(IntDir)$(TargetName).def"
TYPE %(FullPath) &gt;&gt; "$(IntDir)$(TargetName).def"</Command>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release DLL|x64'">ECHO LIBRARY $(TargetName) &gt; "$(IntDir)$(TargetName).def"
ECHO EXPORTS &gt;&gt;  "$(IntDir)$(TargetName).def"
TYPE %(FullPath) &gt;&gt; "$(IntDir)$(TargetName).def"</Command>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug DLL|x64'">ECHO LIBRARY $(TargetName) &gt; "$(IntDir)$(TargetName).def"
ECHO EXPORTS &gt;&gt;  "$(IntDir)$(TargetName).def"
TYPE %(FullPath) &gt;&gt; "$(IntDir)$(TargetName).def"</Command>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">ECHO LIBRARY $(TargetName) &gt; "$(IntDir)$(TargetName).def"
ECHO EXPORTS &gt;&gt;  "$(IntDir)$(TargetName).def"
TYPE %(FullPath) &gt;&gt; "$(IntDir)$(TargetName).def"</Command>
      <Command Condition="'$(Configuration)|$(Platform)'=='NoTLS|x64'">ECHO LIBRARY $(TargetName) &gt; "$(IntDir)$(TargetName).def"
ECHO EXPORTS &gt;&gt;  "$(IntDir)$(TargetName).def"
TYPE %(FullPath) &gt;&gt; "$(IntDir)$(TargetName).def"</Command>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Creating module definition file</Message>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release DLL|Win32'">Creating module definition file</Message>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug DLL|Win32'">Creating module definition file</Message>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Creating module definition file</Message>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Creating module definition file</Message>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release DLL|x64'">Creating module definition file</Message>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug DLL|x64'">Creating module definition file</Message>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Creating module definition file</Message>
      <Message Condition="'$(Configuration)|$(Platform)'=='NoTLS|x64'">Creating module definition file</Message>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">$(IntDir)$(TargetName).def</Outputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release DLL|Win32'">$(IntDir)$(TargetName).def</Outputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug DLL|Win32'">$(IntDir)$(TargetName).def</Outputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">$(IntDir)$(TargetName).def</Outputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(IntDir)$(TargetName).def</Outputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release DLL|x64'">$(IntDir)$(TargetName).def</Outputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug DLL|x64'">$(IntDir)$(TargetName).def</Outputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(IntDir)$(TargetName).def</Outputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='NoTLS|x64'">$(IntDir)$(TargetName).def</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">false</LinkObjects>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release DLL|Win32'">false</LinkObjects>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug DLL|Win32'">false</LinkObjects>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">false</LinkObjects>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release DLL|x64'">false</LinkObjects>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug DLL|x64'">false</LinkObjects>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='NoTLS|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{96A98759-36B3-4246-A265-CAFCEEC0F2F2}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>libcoap</RootNamespace>
    <WindowsTargetPlatformVersion>$(LatestTargetPlatformVersion)</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>$(DefaultPlatformToolset)</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug DLL|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>$(DefaultPlatformToolset)</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>$(DefaultPlatformToolset)</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release DLL|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>$(DefaultPlatformToolset)</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>$(DefaultPlatformToolset)</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug DLL|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>$(DefaultPlatformToolset)</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>$(DefaultPlatformToolset)</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release DLL|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>$(DefaultPlatformToolset)</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='NoTLS|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>$(DefaultPlatformToolset)</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="libcoap.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug DLL|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="libcoap.props" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="libcoap.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release DLL|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="libcoap.props" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="libcoap.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug DLL|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="libcoap.props" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="libcoap.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release DLL|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="libcoap.props" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='NoTLS|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="libcoap.props" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <TargetName>$(ProjectName)$(DbgSuffix)</TargetName>
    <CustomBuildBeforeTargets>
    </CustomBuildBeforeTargets>
    <CustomBuildAfterTargets>
    </CustomBuildAfterTargets>
    <IncludePath>$(IncludePath)</IncludePath>
    <LibraryPath>$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug DLL|Win32'">
    <TargetName>$(ProjectName)-3$(DbgSuffix)-openssl</TargetName>
    <CustomBuildBeforeTargets />
    <CustomBuildAfterTargets />
    <IncludePath>$(IncludePath)</IncludePath>
    <LibraryPath>$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <CustomBuildBeforeTargets>
    </CustomBuildBeforeTargets>
    <CustomBuildAfterTargets>
    </CustomBuildAfterTargets>
    <IncludePath>$(IncludePath)</IncludePath>
    <LibraryPath>$(LibraryPath)</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release DLL|Win32'">
    <CustomBuildBeforeTargets />
    <CustomBuildAfterTargets />
    <TargetName>$(ProjectName)-3-openssl</TargetName>
    <IncludePath>$(IncludePath)</IncludePath>
    <LibraryPath>$(LibraryPath)</LibraryPath>
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug DLL|x64'">
    <TargetName>$(ProjectName)-3$(DbgSuffix)-openssl</TargetName>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);$(SolutionDir)include</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(NETFXKitsDir)Lib\um\x64;$(SolutionDir)lib</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <TargetName>$(ProjectName)$(DbgSuffix)</TargetName>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);$(SolutionDir)include</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(NETFXKitsDir)Lib\um\x64;$(SolutionDir)lib</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release DLL|x64'">
    <TargetName>$(ProjectName)-3-openssl</TargetName>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);$(SolutionDir)include</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(NETFXKitsDir)Lib\um\x64;$(SolutionDir)lib</LibraryPath>
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);$(SolutionDir)include</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(NETFXKitsDir)Lib\um\x64;$(SolutionDir)lib</LibraryPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='NoTLS|x64'">
    <TargetName>$(ProjectName)-3</TargetName>
    <IncludePath>$(VC_IncludePath);$(WindowsSDK_IncludePath);$(SolutionDir)include</IncludePath>
    <LibraryPath>$(VC_LibraryPath_x64);$(WindowsSDK_LibraryPath_x64);$(NETFXKitsDir)Lib\um\x64;$(SolutionDir)lib</LibraryPath>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;_DEBUG;_LIB;COAP_WITH_LIBOPENSSL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <AdditionalIncludeDirectories>../include;..;$(OpenSSLIncludeDirDbg)</AdditionalIncludeDirectories>
      <ProgramDataBaseFileName>$(IntDir)$(TargetName).pdb</ProgramDataBaseFileName>
      <MinimalRebuild>false</MinimalRebuild>
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
    </Link>
    <PreBuildEvent>
      <Command>copy /Y ..\coap_config.h.windows ..\coap_config.h
copy /Y ..\coap_config.h.windows ..\$(LibCoAPIncludeDir)\coap_defines.h</Command>
    </PreBuildEvent>
    <PreBuildEvent>
      <Message>Copying config files for windows</Message>
    </PreBuildEvent>
    <CustomBuildStep>
      <Command>
      </Command>
    </CustomBuildStep>
    <CustomBuildStep>
      <Message>
      </Message>
    </CustomBuildStep>
    <CustomBuildStep>
      <Outputs>
      </Outputs>
    </CustomBuildStep>
    <CustomBuildStep>
      <Inputs>
      </Inputs>
    </CustomBuildStep>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug DLL|Win32'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;_DEBUG;_LIB;COAP_WITH_LIBOPENSSL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <AdditionalIncludeDirectories>../include;..;$(OpenSSLIncludeDirDbg)</AdditionalIncludeDirectories>
      <MinimalRebuild>false</MinimalRebuild>
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ModuleDefinitionFile>$(IntDir)$(TargetName).def</ModuleDefinitionFile>
      <AdditionalDependencies>libcrypto$(DbgSuffix).lib;libssl$(DbgSuffix).lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>$(OpenSSLLibDirDbg)</AdditionalLibraryDirectories>
      <LinkTimeCodeGeneration>Default</LinkTimeCodeGeneration>
    </Link>
    <PreBuildEvent>
      <Command>copy /Y ..\coap_config.h.windows ..\coap_config.h
copy /Y ..\coap_config.h.windows ..\$(LibCoAPIncludeDir)\coap_defines.h</Command>
    </PreBuildEvent>
    <PreBuildEvent>
      <Message>Copying config files for windows</Message>
    </PreBuildEvent>
    <CustomBuildStep>
      <Command>
      </Command>
    </CustomBuildStep>
    <CustomBuildStep>
      <Message>
      </Message>
    </CustomBuildStep>
    <CustomBuildStep>
      <Outputs>
      </Outputs>
    </CustomBuildStep>
    <CustomBuildStep>
      <Inputs>
      </Inputs>
    </CustomBuildStep>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;_DEBUG;_LIB;COAP_WITH_LIBOPENSSL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <AdditionalIncludeDirectories>../include;..;$(OpenSSLIncludeDirDbg)</AdditionalIncludeDirectories>
      <EnableEnhancedInstructionSet>NotSet</EnableEnhancedInstructionSet>
      <CompileAs>CompileAsC</CompileAs>
      <MinimalRebuild>false</MinimalRebuild>
      <ProgramDataBaseFileName>$(IntDir)$(TargetName).pdb</ProgramDataBaseFileName>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
    </Link>
    <PreBuildEvent>
      <Command>copy /Y ..\coap_config.h.windows ..\coap_config.h
copy /Y ..\coap_config.h.windows ..\$(LibCoAPIncludeDir)\coap_defines.h</Command>
      <Message>Copying config files for windows</Message>
    </PreBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug DLL|x64'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;_DEBUG;_LIB;COAP_WITH_LIBOPENSSL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <AdditionalIncludeDirectories>../include;..;$(OpenSSLIncludeDirDbg)</AdditionalIncludeDirectories>
      <EnableEnhancedInstructionSet>NotSet</EnableEnhancedInstructionSet>
      <CompileAs>CompileAsC</CompileAs>
      <MinimalRebuild>false</MinimalRebuild>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <ModuleDefinitionFile>$(IntDir)$(TargetName).def</ModuleDefinitionFile>
      <AdditionalDependencies>libcrypto$(DbgSuffix).lib;libssl$(DbgSuffix).lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>$(OpenSSLLibDirDbg)</AdditionalLibraryDirectories>
    </Link>
    <PreBuildEvent>
      <Command>copy /Y ..\coap_config.h.windows ..\coap_config.h
copy /Y ..\coap_config.h.windows ..\$(LibCoAPIncludeDir)\coap_defines.h</Command>
      <Message>Copying config files for windows</Message>
    </PreBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;NDEBUG;_LIB;COAP_WITH_LIBOPENSSL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>../include;..;$(OpenSSLIncludeDir)</AdditionalIncludeDirectories>
      <ProgramDataBaseFileName>$(IntDir)$(TargetName).pdb</ProgramDataBaseFileName>
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <PreBuildEvent>
      <Command>copy /Y ..\coap_config.h.windows ..\coap_config.h
copy /Y ..\coap_config.h.windows ..\$(LibCoAPIncludeDir)\coap_defines.h</Command>
    </PreBuildEvent>
    <PreBuildEvent>
      <Message>Copying config files for windows</Message>
    </PreBuildEvent>
    <CustomBuildStep>
      <Command>
      </Command>
    </CustomBuildStep>
    <CustomBuildStep>
      <Message>
      </Message>
    </CustomBuildStep>
    <CustomBuildStep>
      <Outputs>
      </Outputs>
    </CustomBuildStep>
    <CustomBuildStep>
      <Inputs>
      </Inputs>
    </CustomBuildStep>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release DLL|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;NDEBUG;_LIB;COAP_WITH_LIBOPENSSL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>../include;..;$(OpenSSLIncludeDir)</AdditionalIncludeDirectories>
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <ModuleDefinitionFile>$(IntDir)$(TargetName).def</ModuleDefinitionFile>
      <AdditionalDependencies>libcrypto.lib;libssl.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>$(OpenSSLLibDir)</AdditionalLibraryDirectories>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <LinkTimeCodeGeneration>UseLinkTimeCodeGeneration</LinkTimeCodeGeneration>
    </Link>
    <PreBuildEvent>
      <Command>copy /Y ..\coap_config.h.windows ..\coap_config.h
copy /Y ..\coap_config.h.windows ..\$(LibCoAPIncludeDir)\coap_defines.h</Command>
    </PreBuildEvent>
    <PreBuildEvent>
      <Message>Copying config files for windows</Message>
    </PreBuildEvent>
    <CustomBuildStep>
      <Command>
      </Command>
    </CustomBuildStep>
    <CustomBuildStep>
      <Message>
      </Message>
    </CustomBuildStep>
    <CustomBuildStep>
      <Outputs>
      </Outputs>
    </CustomBuildStep>
    <CustomBuildStep>
      <Inputs>
      </Inputs>
    </CustomBuildStep>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;NDEBUG;_LIB;COAP_WITH_LIBOPENSSL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>../include;..;$(OpenSSLIncludeDir)</AdditionalIncludeDirectories>
      <EnableEnhancedInstructionSet>NotSet</EnableEnhancedInstructionSet>
      <CompileAs>CompileAsC</CompileAs>
      <ProgramDataBaseFileName>$(IntDir)$(TargetName).pdb</ProgramDataBaseFileName>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <PreBuildEvent>
      <Command>copy /Y ..\coap_config.h.windows ..\coap_config.h
copy /Y ..\coap_config.h.windows ..\$(LibCoAPIncludeDir)\coap_defines.h</Command>
      <Message>Copying config files for windows</Message>
    </PreBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release DLL|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;NDEBUG;_LIB;COAP_WITH_LIBOPENSSL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>../include;..;$(OpenSSLIncludeDir)</AdditionalIncludeDirectories>
      <EnableEnhancedInstructionSet>NotSet</EnableEnhancedInstructionSet>
      <CompileAs>CompileAsC</CompileAs>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <ModuleDefinitionFile>$(IntDir)$(TargetName).def</ModuleDefinitionFile>
      <AdditionalDependencies>libcrypto.lib;libssl.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <LinkTimeCodeGeneration>UseLinkTimeCodeGeneration</LinkTimeCodeGeneration>
      <AdditionalLibraryDirectories>$(OpenSSLLibDir)</AdditionalLibraryDirectories>
    </Link>
    <PreBuildEvent>
      <Command>copy /Y ..\coap_config.h.windows ..\coap_config.h
copy /Y ..\coap_config.h.windows ..\$(LibCoAPIncludeDir)\coap_defines.h</Command>
      <Message>Copying config files for windows</Message>
    </PreBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='NoTLS|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;NDEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>../include;..</AdditionalIncludeDirectories>
      <EnableEnhancedInstructionSet>NotSet</EnableEnhancedInstructionSet>
      <CompileAs>CompileAsC</CompileAs>
      <ProgramDataBaseFileName>$(IntDir)$(TargetName).pdb</ProgramDataBaseFileName>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <PreBuildEvent>
      <Command>copy /Y ..\coap_config.h.windows ..\coap_config.h
copy /Y ..\coap_config.h.windows ..\$(LibCoAPIncludeDir)\coap_defines.h</Command>
      <Message>Copying config files for windows</Message>
    </PreBuildEvent>
  </ItemDefinitionGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
