<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Label="PropertySheets" />
  <PropertyGroup Label="UserMacros">
    <InstallDir>$(SolutionDir)out\</InstallDir>
    <InstallDirDbg>$(SolutionDir)dbg\</InstallDirDbg>
    <DbgSuffix />
    <OpenSSLRootDir>$(OPENSSL_INSTALL_PATH)</OpenSSLRootDir>
    <OpenSSLRootDirDbg>$(OPENSSL_INSTALL_PATH)</OpenSSLRootDirDbg>
    <OpenSSLIncludeDir>$(OpenSSLRootDir)include</OpenSSLIncludeDir>
    <OpenSSLIncludeDirDbg>$(OpenSSLRootDirDbg)include</OpenSSLIncludeDirDbg>
    <OpenSSLLibDir>$(OpenSSLRootDir)lib</OpenSSLLibDir>
    <OpenSSLLibDirDbg>$(OpenSSLRootDirDbg)lib</OpenSSLLibDirDbg>
    <CUnitRootDir>$(InstallDir)</CUnitRootDir>
    <CUnitRootDirDbg>$(InstallDirDbg)</CUnitRootDirDbg>
    <CUnitIncludeDir>$(CUnitRootDir)include</CUnitIncludeDir>
    <CUnitIncludeDirDbg>$(CUnitRootDirDbg)include</CUnitIncludeDirDbg>
    <CUnitLibDir>$(CUnitRootDir)lib</CUnitLibDir>
    <CUnitLibDirDbg>$(CUnitRootDirDbg)lib</CUnitLibDirDbg>
    <LibCoAPIncludeDir>include\coap3</LibCoAPIncludeDir>
    <LibCoAPOSCOREIncludeDir>include\oscore</LibCoAPOSCOREIncludeDir>
  </PropertyGroup>
  <PropertyGroup />
  <ItemDefinitionGroup />
  <ItemGroup>
    <BuildMacro Include="InstallDir">
      <Value>$(InstallDir)</Value>
      <EnvironmentVariable>true</EnvironmentVariable>
    </BuildMacro>
    <BuildMacro Include="InstallDirDbg">
      <Value>$(InstallDirDbg)</Value>
      <EnvironmentVariable>true</EnvironmentVariable>
    </BuildMacro>
    <BuildMacro Include="DbgSuffix">
      <Value>$(DbgSuffix)</Value>
      <EnvironmentVariable>true</EnvironmentVariable>
    </BuildMacro>
    <BuildMacro Include="OpenSSLRootDir">
      <Value>$(OpenSSLRootDir)</Value>
    </BuildMacro>
    <BuildMacro Include="OpenSSLRootDirDbg">
      <Value>$(OpenSSLRootDirDbg)</Value>
    </BuildMacro>
    <BuildMacro Include="OpenSSLIncludeDir">
      <Value>$(OpenSSLIncludeDir)</Value>
      <EnvironmentVariable>true</EnvironmentVariable>
    </BuildMacro>
    <BuildMacro Include="OpenSSLIncludeDirDbg">
      <Value>$(OpenSSLIncludeDirDbg)</Value>
      <EnvironmentVariable>true</EnvironmentVariable>
    </BuildMacro>
    <BuildMacro Include="OpenSSLLibDir">
      <Value>$(OpenSSLLibDir)</Value>
      <EnvironmentVariable>true</EnvironmentVariable>
    </BuildMacro>
    <BuildMacro Include="OpenSSLLibDirDbg">
      <Value>$(OpenSSLLibDirDbg)</Value>
      <EnvironmentVariable>true</EnvironmentVariable>
    </BuildMacro>
    <BuildMacro Include="CUnitRootDir">
      <Value>$(CUnitRootDir)</Value>
    </BuildMacro>
    <BuildMacro Include="CUnitRootDirDbg">
      <Value>$(CUnitRootDirDbg)</Value>
    </BuildMacro>
    <BuildMacro Include="CUnitIncludeDir">
      <Value>$(CUnitIncludeDir)</Value>
      <EnvironmentVariable>true</EnvironmentVariable>
    </BuildMacro>
    <BuildMacro Include="CUnitIncludeDirDbg">
      <Value>$(CUnitIncludeDirDbg)</Value>
      <EnvironmentVariable>true</EnvironmentVariable>
    </BuildMacro>
    <BuildMacro Include="CUnitLibDir">
      <Value>$(CUnitLibDir)</Value>
      <EnvironmentVariable>true</EnvironmentVariable>
    </BuildMacro>
    <BuildMacro Include="CUnitLibDirDbg">
      <Value>$(CUnitLibDirDbg)</Value>
      <EnvironmentVariable>true</EnvironmentVariable>
    </BuildMacro>
  </ItemGroup>
</Project>
