<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\src\coap_address.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\coap_async.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\coap_block.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\coap_cache.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\coap_debug.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\coap_dtls.c">
      <Filter>Source Files</Filter>
    <ClCompile Include="..\src\coap_encode.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\coap_event.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\coap_hashkey.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\coap_gnutls.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\coap_io.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\coap_layers.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\coap_mbedtls.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\coap_mem.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\coap_net.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\coap_netif.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\coap_notls.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\coap_openssl.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\coap_option.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\coap_oscore.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\coap_pdu.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\coap_proxy.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\coap_resource.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\coap_session.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\coap_sha1.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\coap_str.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\coap_subscribe.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\coap_tcp.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\coap_threadsafe.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\coap_time.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\coap_tinydtls.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\coap_uri.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\coap_wolfssl.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\coap_ws.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\oscore\oscore.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\oscore\oscore_cbor.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\oscore\oscore_context.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\oscore\oscore_cose.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\oscore\oscore_crypto.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\coap_config.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\libcoap.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_address.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_asn1_internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_async.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_async_internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_block.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_block_internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_cache.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_cache_internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_crypto_internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_debug.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_debug_internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_dtls.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_dtls_internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_encode.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_event.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_forward_decls.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_hashkey_internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_io.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_io_internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_layers_internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_mem.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_mutex_internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_net.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_net_internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_netif_internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_option.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_oscore.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_oscore_internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_pdu.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_pdu_internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_prng.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_prng_internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_proxy.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_resource.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_resource_internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_session.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_session_internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_sha1_internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_str.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_subscribe.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_subscribe_internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_supported.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_tcp_internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_threadsafe_internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_time.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_uri.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_uri_internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_uthash_internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_utlist_internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_ws.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPIncludeDir)\coap_ws_internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPOSCOREIncludeDir)\oscore_cbor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPOSCOREIncludeDir)\oscbor_context.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPOSCOREIncludeDir)\oscbor_cose.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPOSCOREIncludeDir)\oscore_crypto.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\$(LibCoAPOSCOREIncludeDir)\oscore.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="..\libcoap-3.sym" />
  </ItemGroup>
</Project>
