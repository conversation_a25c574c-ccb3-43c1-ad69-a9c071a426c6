/*
 * Copyright (c) 2009 <PERSON> <<EMAIL>>
 *
 * Copyright (c) 2013 <PERSON><PERSON> <jtri<PERSON><PERSON>@tzi.de>
 * Copyright (c) 2013 <PERSON> <<EMAIL>>
 * Copyright (c) 2013 <PERSON> <<EMAIL>>
 * Copyright (c) 2013 Ha<PERSON> <<EMAIL>>
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 *
 *
 * This implementation is based in part on the paper Implementation of an
 * Elliptic Curve Cryptosystem on an 8-bit Microcontroller [0] by
 * Chris K Cockrum <<EMAIL>>.
 *
 * [0]: http://cockrum.net/Implementation_of_ECC_on_an_8-bit_microcontroller.pdf
 *
 * This is a efficient ECC implementation on the secp256r1 curve for 32 Bit CPU
 * architectures. It provides basic operations on the secp256r1 curve and support
 * for ECDH and ECDSA.
 */
#include "test_helper.h"
#include "ecc.h"
#include <string.h>
#include <stdio.h>
#include <stdlib.h>

void ecc_printNumber(const uint32_t *x, int numberLength){ //here the values are turned to MSB!
	int n;

	for(n = numberLength - 1; n >= 0; n--){
		printf("%08x", x[n]);
	}
	printf("\n");
}

void ecc_setRandom(uint32_t *secret){
	int i;

	for (i = 0; i < arrayLength; ++i)
	{
		secret[i] = rand();
	}
}
const uint32_t ecc_prime_m[8] = {0xffffffff, 0xffffffff, 0xffffffff, 0x00000000,
				 0x00000000, 0x00000000, 0x00000001, 0xffffffff};

							
/* This is added after an static byte addition if the answer has a carry in MSB*/
const uint32_t ecc_prime_r[8] = {0x00000001, 0x00000000, 0x00000000, 0xffffffff,
				 0xffffffff, 0xffffffff, 0xfffffffe, 0x00000000};

#ifdef CONTIKI
void
test_assert(const char *file, int lineno)
{
  printf("Assertion failed: file %s, line %d.\n", file, lineno);
  /*
   * loop for a while;
   * call _reset_vector__();
   */
}
#endif
