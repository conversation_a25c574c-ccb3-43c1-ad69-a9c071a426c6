# Makefile for tinydtls
#
# Copyright (c) 2011-2020 <PERSON> (TZI) and others.
# All rights reserved. This program and the accompanying materials
# are made available under the terms of the Eclipse Public License v1.0
# and Eclipse Distribution License v. 1.0 which accompanies this distribution.
#
# The Eclipse Public License is available at http://www.eclipse.org/legal/epl-v10.html
# and the Eclipse Distribution License is available at 
# http://www.eclipse.org/org/documents/edl-v10.php.
#
# Contributors:
#    <PERSON>  - initial API and implementation
#    Hauke <PERSON> - memory optimization, ECC integration
#

# the library's version
VERSION:=@PACKAGE_VERSION@

# tools
@SET_MAKE@
SHELL = /bin/sh
MKDIR = mkdir
CC=@CC@

abs_builddir = @abs_builddir@
top_builddir = @top_builddir@
top_srcdir:= @top_srcdir@

ECC_SOURCES:= ecc.c testecc.c testfield.c test_helper.c
ECC_HEADERS:= ecc.h test_helper.h
FILES:=Makefile.in Makefile.contiki $(ECC_SOURCES) $(ECC_HEADERS) 
DISTDIR=$(top_builddir)/@PACKAGE_TARNAME@-@PACKAGE_VERSION@

ifeq ("@WITH_CONTIKI@", "1")
include Makefile.contiki
else
ECC_OBJECTS:= $(patsubst %.c, %.o, $(ECC_SOURCES)) ecc_test.o
PROGRAMS:= testecc testfield
CPPFLAGS=@CPPFLAGS@
CFLAGS=-Wall -std=c99 -pedantic @CFLAGS@ @WARNING_CFLAGS@ $(EXTRA_CFLAGS) -DTEST_INCLUDE
LDFLAGS=@LDFLAGS@
LDLIBS=@LIBS@

.PHONY: all dirs clean install distclean .gitignore doc uninstall

.SUFFIXES:
.SUFFIXES:      .c .o

all: $(PROGRAMS)

ecc_test.o:	ecc.c ecc.h
	$(CC) $(CFLAGS) $(CPPFLAGS)  -c -o $@ $<

testecc: ecc_test.o test_helper.o

testfield: ecc_test.o test_helper.o

check:	
	echo DISTDIR: $(DISTDIR)
	echo top_builddir: $(top_builddir)

clean:
	@rm -f $(PROGRAMS) main.o $(LIB) $(ECC_OBJECTS)
	for dir in $(SUBDIRS); do \
		$(MAKE) -C $$dir clean ; \
	done

distclean:	clean
	@rm -rf $(DISTDIR)
	@rm -f *~ $(DISTDIR).tar.gz
endif # WITH_CONTIKI

dist:	$(FILES)
	test -d $(DISTDIR)/ecc || mkdir $(DISTDIR)/ecc
	cp -p $(FILES) $(DISTDIR)/ecc

install:	$(ECC_HEADERS)
	test -d $(DESTDIR)$(includedir)/ecc || mkdir -p $(DESTDIR)$(includedir)/ecc
	$(install) $(ECC_HEADERS) $(DESTDIR)$(includedir)/ecc

uninstall:
	for file in $(ECC_HEADERS); do \
		if test -f $(DESTDIR)$(includedir)/ecc/$$file ; then $(RM) $(DESTDIR)$(includedir)/ecc/$$file ; fi ;\
	done
	if test -d $(DESTDIR)$(includedir)/ecc ; then $(RMDIR) $(DESTDIR)$(includedir)/ecc ; fi

.gitignore:
	echo "core\n*~\n*.[oa]\n*.gz\n*.cap\n$(PROGRAM)\n$(DISTDIR)\n.gitignore" >$@
