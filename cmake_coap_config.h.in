/*
 * cmake_coap_config.h -- cmake configuration for libcoap
 *
 * Copyright (C) 2020      <PERSON> <<EMAIL>>
 * Copyright (C) 2021-2024 Jon Shallow <<EMAIL>>
 *
 * SPDX-License-Identifier: BSD-2-Clause
 *
 * This file is part of the CoAP library libcoap. Please see README for terms
 * of use.
 */

#ifndef COAP_CONFIG_H_
#define COAP_CONFIG_H_

#if ! defined(_WIN32)
#define _GNU_SOURCE
#endif

#include <coap@LIBCOAP_API_VERSION@/coap_defines.h>

/* Define to 1 if you have the <arpa/inet.h> header file. */
#cmakedefine HAVE_ARPA_INET_H @HAVE_ARPA_INET_H@

/* Define to 1 if you have the <assert.h> header file. */
#cmakedefine HAVE_ASSERT_H @HAVE_ASSERT_H@

/* Define to 1 if you have the <byteswap.h> header file. */
#cmakedefine HAVE_BYTESWAP_H @HAVE_BYTESWAP_H@

/* Define to 1 if you have the <dlfcn.h> header file. */
#cmakedefine HAVE_DLFCN_H @HAVE_DLFCN_H@

/* Define to 1 if you have the <sys/epoll.h> header file. */
#cmakedefine HAVE_EPOLL_H @HAVE_EPOLL_H@

/* Define to 1 if you have the <errno.h> header file. */
#cmakedefine HAVE_ERRNO_H @HAVE_ERRNO_H@

/* Define to 1 if you have the <float.h> header file. */
#cmakedefine HAVE_FLOAT_H @HAVE_FLOAT_H@

/* Define to 1 if you have the `getaddrinfo' function. */
#cmakedefine HAVE_GETADDRINFO @HAVE_GETADDRINFO@

/* Define to 1 if you have the `getrandom' function. */
#cmakedefine HAVE_GETRANDOM @HAVE_GETRANDOM@

/* Define to 1 if you have the <ifaddrs.h> header file. */
#cmakedefine HAVE_IFADDRS_H @HAVE_IFADDRS_H@

/* Define to 1 if you have the `if_nametoindex' function. */
#cmakedefine HAVE_IF_NAMETOINDEX @HAVE_IF_NAMETOINDEX@

/* Define to 1 if you have the <inttypes.h> header file. */
#cmakedefine HAVE_INTTYPES_H @HAVE_INTTYPES_H@

/* Define to 1 if you have the <limits.h> header file. */
#cmakedefine HAVE_LIMITS_H @HAVE_LIMITS_H@

/* Define to 1 if you have the `malloc' function. */
#cmakedefine HAVE_MALLOC @HAVE_MALLOC@

/* Define to 1 if you have the <memory.h> header file. */
#cmakedefine HAVE_MEMORY_H @HAVE_MEMORY_H@

/* Define to 1 if you have the `memset' function. */
#cmakedefine HAVE_MEMSET @HAVE_MEMSET@

/* Define to 1 if you have the <netdb.h> header file. */
#cmakedefine HAVE_NETDB_H @HAVE_NETDB_H@

/* Define to 1 if you have the <net/if.h> header file. */
#cmakedefine HAVE_NET_IF_H @HAVE_NET_IF_H@

/* Define to 1 if you have the <netinet/in.h> header file. */
#cmakedefine HAVE_NETINET_IN_H @HAVE_NETINET_IN_H@

/* Define to 1 if you have the <pthread.h> header file. */
#cmakedefine HAVE_PTHREAD_H @HAVE_PTHREAD_H@

/* Define to 1 if you have the `pthread_mutex_lock' function. */
#cmakedefine HAVE_PTHREAD_MUTEX_LOCK @HAVE_PTHREAD_MUTEX_LOCK@

/* Define to 1 if you have the `select' function. */
#cmakedefine HAVE_SELECT @HAVE_SELECT@

/* Define to 1 if you have the `socket' function. */
#cmakedefine HAVE_SOCKET @HAVE_SOCKET@

/* Define to 1 if you have the <stdbool.h> header file. */
#cmakedefine HAVE_STDBOOL_H @HAVE_STDBOOL_H@

/* Define to 1 if you have the <stddef.h> header file. */
#cmakedefine HAVE_STDDEF_H @HAVE_STDDEF_H@

/* Define to 1 if you have the <stdint.h> header file. */
#cmakedefine HAVE_STDINT_H @HAVE_STDINT_H@

/* Define to 1 if you have the <stdlib.h> header file. */
#cmakedefine HAVE_STDLIB_H @HAVE_STDLIB_H@

/* Define to 1 if you have the `strcasecmp' function. */
#cmakedefine HAVE_STRCASECMP @HAVE_STRCASECMP@

/* Define to 1 if you have the <strings.h> header file. */
#cmakedefine HAVE_STRINGS_H @HAVE_STRINGS_H@

/* Define to 1 if you have the <string.h> header file. */
#cmakedefine HAVE_STRING_H @HAVE_STRING_H@

/* Define to 1 if you have the `strnlen' function. */
#cmakedefine HAVE_STRNLEN @HAVE_STRNLEN@

/* Define to 1 if you have the `strrchr' function. */
#cmakedefine HAVE_STRRCHR @HAVE_STRRCHR@

/* Define to 1 if you have the `randon' function. */
#cmakedefine HAVE_RANDOM @HAVE_RANDOM@

/* Define to 1 if the system has the type `struct cmsghdr'. */
#cmakedefine HAVE_STRUCT_CMSGHDR @HAVE_STRUCT_CMSGHDR@

/* Define to 1 if you have the <sys/ioctl.h> header file. */
#cmakedefine HAVE_SYS_IOCTL_H @HAVE_SYS_IOCTL_H@

/* Define to 1 if you have the <sys/socket.h> header file. */
#cmakedefine HAVE_SYS_SOCKET_H @HAVE_SYS_SOCKET_H@

/* Define to 1 if you have the <sys/stat.h> header file. */
#cmakedefine HAVE_SYS_STAT_H @HAVE_SYS_STAT_H@

/* Define to 1 if you have the <sys/sysctl.h> header file. */
#cmakedefine HAVE_SYS_SYSCTL_H @HAVE_SYS_SYSCTL_H@

/* Define to 1 if you have the <sys/time.h> header file. */
#cmakedefine HAVE_SYS_TIME_H @HAVE_SYS_TIME_H@

/* Define to 1 if you have the <sys/types.h> header file. */
#cmakedefine HAVE_SYS_TYPES_H @HAVE_SYS_TYPES_H@

/* Define to 1 if you have the <sys/unistd.h> header file. */
#cmakedefine HAVE_SYS_UNISTD_H @HAVE_SYS_UNISTD_H@

/* Define to 1 if you have the <time.h> header file. */
#cmakedefine HAVE_TIME_H @HAVE_TIME_H@

/* Define to 1 if you have the <sys/timerfd.h> header file. */
#cmakedefine HAVE_TIMERFD_H @HAVE_TIMERFD_H@

/* Define to 1 if you have the <unistd.h> header file. */
#cmakedefine HAVE_UNISTD_H @HAVE_UNISTD_H@

/* Define to 1 if you have <winsock2.h> header file. */
#cmakedefine HAVE_WINSOCK2_H @HAVE_WINSOCK2_H@

/* Define to 1 if you have <ws2tcpip.h> header file. */
#cmakedefine HAVE_WS2TCPIP_H @HAVE_WS2TCPIP_H@

/* Define to the address where bug reports for this package should be sent. */
#cmakedefine PACKAGE_BUGREPORT "@PACKAGE_BUGREPORT@"

/* Define to the full name of this package. */
#cmakedefine PACKAGE_NAME "@PACKAGE_NAME@"

/* Define to the full name and version of this package. */
#cmakedefine PACKAGE_STRING "@PACKAGE_STRING@"

/* Define to the one symbol short name of this package. */
#cmakedefine PACKAGE_TARNAME "@PACKAGE_TARNAME@"

/* Define to the home page for this package. */
#cmakedefine PACKAGE_URL "@PACKAGE_URL@"

/* Define to the version of this package. */
#cmakedefine PACKAGE_VERSION "@PACKAGE_VERSION@"

#if defined(_MSC_VER) && (_MSC_VER < 1900) && !defined(snprintf)
#define snprintf _snprintf
#endif

#endif /* COAP_CONFIG_H_ */
