coap_add_attr
coap_add_block
coap_add_block_b_data
coap_add_data
coap_add_data_after
coap_add_data_blocked_response
coap_add_data_large_request
coap_add_data_large_response
coap_add_option
coap_add_optlist_pdu
coap_add_resource
coap_add_token
coap_address_copy
coap_address_equals
coap_address_get_port
coap_address_init
coap_address_set_port
coap_address_set_unix_domain
coap_af_unix_is_supported
coap_async_get_app_data
coap_async_is_supported
coap_async_set_app_data
coap_async_set_delay
coap_async_trigger
coap_attr_get_value
coap_block_build_body
coap_cache_derive_key
coap_cache_derive_key_w_ignore
coap_cache_get_app_data
coap_cache_get_by_key
coap_cache_get_by_pdu
coap_cache_get_pdu
coap_cache_ignore_options
coap_cache_set_app_data
coap_can_exit
coap_cancel_observe
coap_check_notify
coap_check_option
coap_cleanup
coap_clear_event_handler
coap_client_is_supported
coap_clock_init
coap_clone_uri
coap_context_get_app_data
coap_context_get_coap_fd
coap_context_get_csm_max_message_size
coap_context_get_csm_timeout
coap_context_get_csm_timeout_ms
coap_context_get_max_handshake_sessions
coap_context_get_max_idle_sessions
coap_context_get_session_timeout
coap_context_oscore_server
coap_context_set_app_data
coap_context_set_block_mode
coap_context_set_cid_tuple_change
coap_context_set_csm_max_message_size
coap_context_set_csm_timeout
coap_context_set_csm_timeout_ms
coap_context_set_keepalive
coap_context_set_max_block_size
coap_context_set_max_handshake_sessions
coap_context_set_max_idle_sessions
coap_context_set_max_token_size
coap_context_set_pki
coap_context_set_pki_root_cas
coap_context_set_psk
coap_context_set_psk2
coap_context_set_session_timeout
coap_debug_set_packet_loss
coap_decode_var_bytes
coap_decode_var_bytes8
coap_delete_bin_const
coap_delete_binary
coap_delete_cache_entry
coap_delete_cache_key
coap_delete_optlist
coap_delete_oscore_conf
coap_delete_oscore_recipient
coap_delete_pdu
coap_delete_resource
coap_delete_str_const
coap_delete_string
coap_delete_uri
coap_dtls_cid_is_supported
coap_dtls_get_log_level
coap_dtls_is_supported
coap_dtls_pkcs11_is_supported
coap_dtls_pki_is_supported
coap_dtls_psk_is_supported
coap_dtls_rpk_is_supported
coap_dtls_set_log_level
coap_dump_memory_type_counts
coap_encode_var_safe
coap_encode_var_safe8
coap_endpoint_set_default_mtu
coap_endpoint_str
coap_epoll_is_supported
coap_find_async
coap_find_attr
coap_fls
coap_flsll
coap_free_address_info
coap_free_async
coap_free_context
coap_free_endpoint
coap_free_type
coap_get_app_data
coap_get_available_scheme_hint_bits
coap_get_block
coap_get_block_b
coap_get_data
coap_get_data_large
coap_get_log_level
coap_get_query
coap_get_resource_from_uri_path
coap_get_tls_library_version
coap_get_uri_path
coap_handle_event
coap_host_is_unix_domain
coap_insert_optlist
coap_io_do_epoll
coap_io_do_io
coap_io_pending
coap_io_prepare_epoll
coap_io_prepare_io
coap_io_process
coap_io_process_with_fds
coap_ipv4_is_supported
coap_ipv6_is_supported
coap_is_af_unix
coap_is_bcast
coap_is_mcast
coap_join_mcast_group_intf
coap_log_impl
coap_log_level_desc
coap_make_str_const
coap_malloc_type
coap_mcast_per_resource
coap_mcast_set_hops
coap_memory_init
coap_new_bin_const
coap_new_binary
coap_new_cache_entry
coap_new_client_session
coap_new_client_session_oscore
coap_new_client_session_oscore_pki
coap_new_client_session_oscore_psk
coap_new_client_session_pki
coap_new_client_session_psk
coap_new_client_session_psk2
coap_new_context
coap_new_endpoint
coap_new_error_response
coap_new_message_id
coap_new_optlist
coap_new_oscore_conf
coap_new_oscore_recipient
coap_new_pdu
coap_new_str_const
coap_new_string
coap_new_uri
coap_observe_persist_is_supported
coap_opt_block_num
coap_opt_encode
coap_opt_encode_size
coap_opt_length
coap_opt_parse
coap_opt_setheader
coap_opt_size
coap_opt_value
coap_option_filter_clear
coap_option_filter_get
coap_option_filter_set
coap_option_filter_unset
coap_option_iterator_init
coap_option_next
coap_oscore_is_supported
coap_package_build
coap_package_name
coap_package_version
coap_path_into_optlist
coap_pdu_duplicate
coap_pdu_get_code
coap_pdu_get_mid
coap_pdu_get_token
coap_pdu_get_type
coap_pdu_init
coap_pdu_parse
coap_pdu_set_code
coap_pdu_set_mid
coap_pdu_set_type
coap_persist_observe_add
coap_persist_set_observe_num
coap_persist_startup
coap_persist_stop
coap_persist_track_funcs
coap_print_addr
coap_print_ip_addr
coap_print_link
coap_print_wellknown
coap_prng
coap_prng_init
coap_proxy_forward_request
coap_proxy_forward_response
coap_proxy_is_supported
coap_q_block_is_supported
coap_query_into_optlist
coap_realloc_type
coap_register_async
coap_register_event_handler
coap_register_handler
coap_register_nack_handler
coap_register_option
coap_register_ping_handler
coap_register_pong_handler
coap_register_request_handler
coap_register_response_handler
coap_resize_binary
coap_resolve_address_info
coap_resource_get_uri_path
coap_resource_get_userdata
coap_resource_init
coap_resource_notify_observers
coap_resource_proxy_uri_init
coap_resource_proxy_uri_init2
coap_resource_release_userdata_handler
coap_resource_reverse_proxy_init
coap_resource_set_dirty
coap_resource_set_get_observable
coap_resource_set_mode
coap_resource_set_userdata
coap_resource_unknown_init
coap_resource_unknown_init2
coap_response_phrase
coap_send
coap_send_ack
coap_send_error
coap_send_message_type
coap_send_rst
coap_server_is_supported
coap_session_disconnected
coap_session_get_ack_random_factor
coap_session_get_ack_timeout
coap_session_get_addr_local
coap_session_get_addr_mcast
coap_session_get_addr_remote
coap_session_get_app_data
coap_session_get_by_peer
coap_session_get_context
coap_session_get_default_leisure
coap_session_get_ifindex
coap_session_get_max_payloads
coap_session_get_max_retransmit
coap_session_get_non_max_retransmit
coap_session_get_non_receive_timeout
coap_session_get_non_timeout
coap_session_get_nstart
coap_session_get_probing_rate
coap_session_get_proto
coap_session_get_psk_hint
coap_session_get_psk_identity
coap_session_get_psk_key
coap_session_get_state
coap_session_get_tls
coap_session_get_type
coap_session_init_token
coap_session_max_pdu_size
coap_session_new_token
coap_session_reference
coap_session_release
coap_session_send_ping
coap_session_set_ack_random_factor
coap_session_set_ack_timeout
coap_session_set_app_data
coap_session_set_default_leisure
coap_session_set_max_payloads
coap_session_set_max_retransmit
coap_session_set_mtu
coap_session_set_no_observe_cancel
coap_session_set_non_max_retransmit
coap_session_set_non_receive_timeout
coap_session_set_non_timeout
coap_session_set_nstart
coap_session_set_probing_rate
coap_session_set_type_client
coap_session_str
coap_set_app_data
coap_set_event_handler
coap_set_log_handler
coap_set_log_level
coap_set_prng
coap_set_show_pdu_output
coap_show_pdu
coap_show_tls_version
coap_socket_strerror
coap_split_path
coap_split_proxy_uri
coap_split_query
coap_split_uri
coap_startup
coap_string_tls_support
coap_string_tls_version
coap_tcp_is_supported
coap_threadsafe_is_supported
coap_ticks
coap_ticks_from_rt_us
coap_ticks_to_rt
coap_ticks_to_rt_us
coap_tls_engine_configure
coap_tls_engine_remove
coap_tls_is_supported
coap_uri_into_options
coap_uri_into_optlist
coap_verify_proxy_scheme_supported
coap_write_block_b_opt
coap_write_block_opt
coap_ws_is_supported
coap_ws_set_host_request
coap_wss_is_supported
