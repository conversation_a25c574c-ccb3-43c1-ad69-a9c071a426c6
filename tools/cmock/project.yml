# Taken from amazon-freertos repository
:cmock:
  :mock_prefix: mock_
  :when_no_prototypes: :warn
  :enforce_strict_ordering: TRUE
  :plugins:
    - :ignore
    - :ignore_arg
    - :expect_any_args
    - :array
    - :callback
    - :return_thru_ptr
  :callback_include_count: true # include a count arg when calling the callback
  :callback_after_arg_check: false # check arguments before calling the callback
  :treat_as:
    uint8:    HEX8
    uint16:   HEX16
    uint32:   UINT32
    int8:     INT8
    bool:     UINT8
  :includes:        # This will add these includes to each mock.
    - <stdbool.h>
    - <stdint.h>
  :treat_externs: :exclude  # Now the extern-ed functions will be mocked.
  :weak: __attribute__((weak))
  :treat_externs: :include
