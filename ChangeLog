2024-05-23  <PERSON>  <<EMAIL>>

	Change summary for version 4.3.5:

        * Support for wolfSSL TLS library.
        * Support for DTLS1.3 (using wolfSSL).
        * Support for Mbed TLS 3.6.0.
        * Support for EC-JPAKE (Mbed TLS)
        * TinyDTLS version update.
        * Support for RIOT using SOCK i/f.
        * Support for LwIP 2.2.0.
        * Support for LwIP using NO_SYS set to 0.
        * Support for (Posix based) Zephyr.
        * Support for QNX builds.
        * Support for ESP32 xtensa builds.
        * Updated Contiki-NG support.
        * Support for multi-thread safe libcoap usage.
        * Support for defining binary PSK for coap-client and coap-server.
        * Support for Connection-ID (CID) (Mbed TLS, wolfSSL and TinyDTLS).
        * Added new define types for defining PKI parameters.
        * Support for user definable ENGINE for OpenSSL.
        * Support for using noTLS and TinyDTLS with WebSockets.
        * Support for providing list of compilation #defines.
        * Support for proxy code running within lbcoap.
        * Cleaned up support for building .h files.
        * Additional scan-build and pre-commit checks in build tests.
        * Updated CI build tests to use latest action versions.
        * Fixes CVE-2023-35862.
        * Reported bugs fixed.
        * Documentation added and updated (Doxy<PERSON> and man).

2023-09-18  <PERSON>  <<EMAIL>>

	Change summary for version 4.3.4:

        * Clean up use of tags.
        * Support for MacOS with Contiki-NG builds.
        * Support for Windows with OpenSSL 3.x builds.
        * Reported bugs fixed.
        * Documentation updated.

2023-09-12  Olaf Bergmann  <<EMAIL>>

	Change summary for version 4.3.3:

        * Fix ABI version.

2023-07-13  Olaf Bergmann  <<EMAIL>>

	Change summary for version 4.3.2:

        * Source files reformatted according to pre-commit rules.
        * Support for RFC8613 (OSCORE).
        * Support for RFC8974 (Extended Tokens).
        * Support for RFC9177 (Q-Block).
        * Support for latest RIOT code and new examples.
        * Support for MinGW builds.
        * Support for AF_UNIX sockets.
        * Support for WebSockets (RFC8323).
        * Support for IPv4 only and IPv6 only libcoap builds.
        * Support for defining maximum logging level.
        * Support for maintaining Observer requests over server restarts.
        * Support for Contiki-NG.
        * Support for latest LwIP, including using TinyDTLS.
        * libcoap now has protocol layered support, separating out the
          logical layers. Stack now is:-
            - Application
            - libcoap - CoAP
            - libcoap - CoAP-Session
            - libcoap - (D)TLS I/F using external (D)TLS Library
            - libcoap - Netif
            - libcoap - Sockets
            - Kernel Network Stack
        * Fixes CVE-2023-30362 and CVE-2023-35862.
        * Reported bugs fixed.
        * Examples now support separate logging levels for libcoap and (D)TLS.
        * syslog LOG_ logging levels replaced with COAP_LOG_ logging levels.
        * New public API functions to aid / reduce application coding.
        * Remove requirement for applications to have sockaddr knowledge.
        * Support for clients sending IPv4 broadcast requests.
        * Documentation added and updated (Doxygen and man).

2022-08-17  Olaf Bergmann  <<EMAIL>>

	Change summary for version 4.3.1:

        * Support for Server only and Client only libcoap builds.
        * Add support for repeating requests in coap-client.
        * Add in support for defining resources that support multicast.
        * Add in more support for async delayed requests.
        * Add in support for not closing down Observe when closing session.
        * Support for RFC7390, RFC8516 and RFC9175.
        * Warn when Tokens are re-used.
        * Warn when Options are repeated that are not defined as being
          repeatable.
        * Support for TLS when using Mbed TLS library.
        * Support for Mbed TLS 3.1
        * Add in BERT support for block handling.
        * More rigorous error handling for Block transfers.
        * Support for using external or submodule TinyDTLS.
        * Cmake - add in Apple build support.
        * Source files renamed to be more consistent in naming.
        * Update native Windows VC builds to use libcoap-3 instead of libcoap-2.
        * Reported bugs fixed.
        * Example applications Help report include build version.
        * Documentation added and updated (Doxygen and man).

2021-05-04  Olaf Bergmann  <<EMAIL>>

	Change summary for version 4.3.0:

        * Include directory updated from include/coap2 to include/coap3 as
          this is a major version change.
            * Other code references updated from coap2 to coap3.
        * Examples now have the underlying (D)TLS library name as a suffix.
          E.g. coap-server-openssl
        * Examples and libraries can be installed with default names using
          ./configure --enable-add-default-names
        * Many call-back handlers have had their parameter lists changed, some
          variables are made const and other ones removed as they can be easily
          reconstructed if needed.
        * Some functions have their parameters changed to const.
        * Internal structures made opaque to applications, requiring the
          applications to access the structure information via a new set of
          functions.  The new functions are of the form coap_X_get_Y() or
          coap_X_set_Y() where X is  the structure (e.g. session) and Y is
          the variable.
            * coap_async_state_t
            * coap_attr_t
            * coap_context_t
            * coap_packet_t
            * coap_pdu_t
            * coap_resource_t
            * coap_session_t
            * coap_socket_t
        * Header files are moved into libcoap space and so are accessed by coap
          sub-directory - e.g.  #include <coap3/coap.h>.
        * RFC7959 (Block handling) moved into libcoap from application code
          considerably simplifying application code. See coap_block(3) man page.
        * CoAP Cache Key support.
        * Support for cmake builds.
        * Support for RIOT builds.
        * Support for Cygwin builds.
        * Proxy support for coap-server, enhanced coap-client capabilities
        * Updated async support.
            * Multicast requests now randomly delayed before the response is
              sent.
        * Additional RFC support - RFC8768.
        * Mbed TLS DTLS library support.
        * (D)TLS support for RPK and PKCS11.
        * Additional (D)TLS support for PSK (e.g. Identity Hints).
        * PKI support consistent across all supported (D)TLS libraries.
        * Support for disabling TCP for code reduction.
        * More rigorous checking and appropriate rejection of inbound PDU
          options.
        * Additional unit tests.
        * Reported bugs fixed.
        * Example applications help reports on (D)TLS library capabilities
          and versions.
        * Documentation added and updated (Doxygen and man).

2019-11-05  Olaf Bergmann  <<EMAIL>>

	Change summary for version 4.2.1:

        * Builds now support silent rules
        * Support building with TinyDTLS as a shared library
        * Added in EPOLL support
        * Added in support for constrained stacks
        * Server sessions hashed for performance lookup
        * coap_endpoint_t and coap_subscription_t made opaque to applications
        * Documentation updated

2019-02-11  Olaf Bergmann  <<EMAIL>>

	Change summary for version 4.2.0:

	* DTLS support improvements (OpenSSL, GnuTLS, tinydtls)
	    * Pre-shared keys, X.509 certificates
        * new session abstraction
	* TCP and TLS support
	* improved documentation; manual pages
	* changes in internal PDU structure
	* improved examples (DTLS usage, block-wise transfer)
	* docker images for continuous integration
	* support for Google OSS fuzzer
	* MS Visual Studio project for Windows builds

2017-07-10  Olaf Bergmann  <<EMAIL>>

	* DTLS support (OpenSSL, tinyDTLS) by Jean-Claude Michelou
	* Win32 support by Jean-Claude Michelou
	* New Session API by Jean-Claude Michelou

2016-02-16  Olaf Bergmann  <<EMAIL>>

	* Fixed build for Contiki3 and LwIP
	* .travis.yml: Enabled continuous integration for platforms
	  POSIX and Contiki

2015-03-11  Olaf Bergmann  <<EMAIL>>

	* include/coap/resource.h: Replaced custom list structures by
	utlist macros.

2015-03-09  Olaf Bergmann  <<EMAIL>>

	* src/uri.c (coap_split_path): Fixed URI parser bug and
	removed broken parse iterator.

2015-03-05  Olaf Bergmann  <<EMAIL>>

	* src/coap_time.c (coap_ticks): Changed POSIX implementation
	to fixed point arithmetic and removed clock_offset.

2015-02-21  Olaf Bergmann  <<EMAIL>>

	* net.c (coap_send_confirmed): Use fixed point arithmetic
	to calculate retransmission timeout.

2015-02-20  Olaf Bergmann  <<EMAIL>>

	* coap_list.[hc]: Moved old list implementation into
	sub-directory examples and replaced by linked lists
	from utlist.h. As a result, the list must be sorted
	explicitly with LL_SORT).

2015-02-19  Olaf Bergmann  <<EMAIL>>

	* net.c (coap_send_confirmed): Fixed retransmission timeout
	calculation and renamed transmission parameters according to
	Section 4.8 of RFC 7252.

2015-02-17  Olaf Bergmann  <<EMAIL>>

	* major rework to get Contiki and lwip running
	* many fixed bugs and warnings

2014-06-18  Olaf Bergmann  <<EMAIL>>

	* mem.c (coap_malloc_type): New functions for allocating memory.
	On POSIX systems, coap_malloc_type() and coap_free_type() are just
	wrapper functions for malloc() and free(), while on Contiki and
	LWIP distinct arrays are used for each type.

2014-03-09  Olaf Bergmann  <<EMAIL>>

	* net.c (coap_cancel): Removed 7.31 again and implemented new
	method for cancelling observe relationships.

2014-02-25  Olaf Bergmann  <<EMAIL>>

	* net.c (coap_cancel): Handling of 7.31 responses to cancel
	notifications (see Section 4.6 of draft-ietf-core-observe-12)

2014-02-04  Olaf Bergmann  <<EMAIL>>

	* resource.c (coap_print_link): This function now takes an offset
	where printing starts. This is used for generating blocks on the
	fly.

	* net.c (wellknown_response): Added support for Block2 options
	when generating a response for .well-known/core.

	* block.h (coap_opt_block_num): Fixed handling of zero-length
	options. COAP_OPT_BLOCK_LAST now returns NULL when the option
	value's length is zero.

2014-01-07  Olaf Bergmann  <<EMAIL>>

	* resource.c (coap_print_link): Output partial resource
	descriptions. The function now provides a sliding window over the
	textual representation of the resource. Output starts at the given
	offset and ends at the buffer's upper bound. The meaning of the
	return value has changed to allow distinguishing whether or not
	the resource description has been truncated at the buffer's upper
	bound.
	(print_wellknown): Support for the new coap_print_link(). An
	additional parameter now is used to provide the offset into the
	resource description. The meaning of the return value has been
	adjusted accordingly.

2013-12-23  Olaf Bergmann  <<EMAIL>>

	* configure.in: merged with LWIP port from chrysn
	<https://git.gitorious.org/coap-lwip/coap-lwip.git>. This
	introduces new compiler flags WITH_POSIX and WITH_LWIP to
	distinguish target platforms.

2013-09-03  Olaf Bergmann  <<EMAIL>>

	* option.h (coap_option_setb): increased size of option type
	argument

	* tests/test_error_response.c (t_init_error_response_tests): new
	tests for error response generation

	* tests/test_pdu.c (t_encode_pdu5): fixed number for option Accept

	* net.c (coap_new_error_response): fixed option size calculation

2013-07-04  Olaf Bergmann  <<EMAIL>>

	* net.c (coap_new_context): register critical Accept option

	* pdu.c: option codes for Accept and Size1 according to coap-18

2013-02-01  Olaf Bergmann  <<EMAIL>>

	* coap_time.h (coap_clock_init_impl): fix invalid preprocessor
	directive. #warning is now only used for gcc only (close sf bug #15)

	* net.c (wellknown_response): applied patch from chrysn to
	fix bug in generation of .well-known/core representation

2013-01-21  Olaf Bergmann  <<EMAIL>>

	* option.h: renamed option field in coap_opt_iterator_t to
	next_option to detect erroneous use in old code

2013-01-18  Olaf Bergmann  <<EMAIL>>

	* configure.in: new option --with-tests to enable unit tests

	* tests/testdriver.c: unit tests for parser functions

	* pdu.c (coap_pdu_parse): new PDU parser for Klaus-encoding
	according to coap-13

	* net.c (coap_read): call coap_pdu_parse() to check PDU integrity

	* option.c: Klaus-encoding for coap-13, including new option
	iterator interface

2012-11-20  Olaf Bergmann  <<EMAIL>>

	* net.c (next_option_safe): made option parsing more robust in
	presence of option jumps

	* pdu.h: new option codes from draft-ietf-core-coap-12

	* option.c (coap_opt_setlength): new function to set option length

	* uri.c (make_decoded_option): use coap_opt_setlength() instead of
	obsolete macro COAP_OPT_SETLENGTH.

2012-11-19  Olaf Bergmann  <<EMAIL>>

	* uri.c (make_decoded_option): use coap_opt_encode() instead of writing

2012-11-03  Olaf Bergmann  <<EMAIL>>

	* net.c (coap_read): read new option encoding

2012-11-01  Olaf Bergmann  <<EMAIL>>

	* option.c (coap_opt_size, coap_opt_value, coap_opt_length):
	several functions to access fields of options (possibly preceeded
	by option jump)

2012-10-25  Olaf Bergmann  <<EMAIL>>

	* option.c (coap_opt_encode): new function for option encoding
	with option jumps

2012-03-23  Olaf Bergmann  <<EMAIL>>

	* examples/client.c (clear_obs): clear observation relationship after
	user-specified duration

2012-03-21  Olaf Bergmann  <<EMAIL>>

	* resource.c (print_wellknown): filtering by attributes

2012-03-19  Olaf Bergmann  <<EMAIL>>

	* pdu.c (coap_add_option): allow more than 15 options.

2012-03-15  Olaf Bergmann  <<EMAIL>>

	* examples/client.c (cmdline_uri): split path and query here to
	make it easier to include these options in subsequent requests for
	block transfer.

2012-03-14  Olaf Bergmann  <<EMAIL>>

	* examples/etsi_iot_01.c: Support for POST, PUT, DELETE on /test

2012-03-13  Olaf Bergmann  <<EMAIL>>

	* encode.c (coap_encode_var_bytes): more efficient coding for 0

2012-03-11  Olaf Bergmann  <<EMAIL>>

	* examples/etsi_iot_01.c: Test cases for 1st ETSI CoAP Plugtest,
	March 24/25, 2012 in Paris, France.

2012-03-10  Olaf Bergmann  <<EMAIL>>

	* block.c: support for block transfer.

2012-03-07  Olaf Bergmann  <<EMAIL>>

	* examples/client.c (usage): new command line options
	-B to set timeout after which the main loop is left.
	-e to specify a payload (incompatible with -f)
	(message_handler): bugfixes

	* resource.h: (coap_method_handler_t): new API for method handlers.


Copyright 2012 Olaf Bergmann, TZI
Copying and distribution of this file, with or without modification, are
permitted provided the copyright notice and this notice are preserved.
