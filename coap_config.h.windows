/*
 * coap_config.h.windows -- Windows configuration for libcoap
 *
 * Copyright (C) 2017-2024 <PERSON> <berg<PERSON>@tzi.org> and others
 *
 * SPDX-License-Identifier: BSD-2-Clause
 *
 * This file is part of the CoAP library libcoap. Please see README for terms
 * of use.
 */

#ifndef COAP_CONFIG_H_
#define COAP_CONFIG_H_

#if defined(_WIN32)

/* Define to 1 if you have <ws2tcpip.h> header file. */
#define HAVE_WS2TCPIP_H 1

/* Define to 1 if you have <winsock2.h> header file. */
#define HAVE_WINSOCK2_H 1

/* Define to 1 if you have the <assert.h> header file. */
#define HAVE_ASSERT_H 1

/* Define to 1 if you have the `getaddrinfo' function. */
#define HAVE_GETADDRINFO 1

/* Define to 1 if you have the <inttypes.h> header file. */
#define HAVE_INTTYPES_H 1

/* Define to 1 if you have the <limits.h> header file. */
#define HAVE_LIMITS_H 1

/* Define to 1 if you have the `malloc' function. */
#define HAVE_MALLOC 1

/* Define to 1 if you have the <memory.h> header file. */
#define HAVE_MEMORY_H 1

/* Define to 1 if you have the `memset' function. */
#define HAVE_MEMSET 1

/* Define to 1 if you have the `select' function. */
#define HAVE_SELECT 1

/* Define to 1 if you have the `socket' function. */
#define HAVE_SOCKET 1

/* Define to 1 if you have the <stdint.h> header file. */
#define HAVE_STDINT_H 1

/* Define to 1 if you have the <stdlib.h> header file. */
#define HAVE_STDLIB_H 1

/* Define to 1 if you have the <string.h> header file. */
#define HAVE_STRING_H 1

/* Define to 1 if you have the `strnlen' function. */
#define HAVE_STRNLEN 1

/* Define to 1 if you have the `strrchr' function. */
#define HAVE_STRRCHR 1

/* Define to 1 if the system has the type `struct cmsghdr'. */
#define HAVE_STRUCT_CMSGHDR 1

/* Define to 1 if you have the <sys/stat.h> header file. */
#define HAVE_SYS_STAT_H 1

/* Define to 1 if you have the <sys/types.h> header file. */
#define HAVE_SYS_TYPES_H 1

/* Define to 1 if you have the <time.h> header file. */
#define HAVE_TIME_H 1

#if defined(_MSC_VER) && (_MSC_VER < 1900) && !defined(snprintf)
#define snprintf _snprintf
#endif

#ifndef COAP_DISABLE_TCP
/* Define to 1 to build without TCP support. */
#define COAP_DISABLE_TCP 0
#endif

#ifndef COAP_IPV4_SUPPORT
/* Define to 1 to build with IPv4 support. */
#define COAP_IPV4_SUPPORT 1
#endif

#ifndef COAP_IPV6_SUPPORT
/* Define to 1 to build with IPv6 support. */
#define COAP_IPV6_SUPPORT 1
#endif

#ifndef COAP_AF_UNIX_SUPPORT
/* Define to 1 to build with Unix socket support. */
#define COAP_AF_UNIX_SUPPORT 0
#endif

#ifndef COAP_CLIENT_SUPPORT
/* Define if libcoap supports client mode code. */
#define COAP_CLIENT_SUPPORT 1
#endif

#ifndef COAP_SERVER_SUPPORT
/* Define if libcoap supports server mode code. */
#define COAP_SERVER_SUPPORT 1
#endif

#if COAP_CLIENT_SUPPORT && COAP_SERVER_SUPPORT
#ifndef COAP_PROXY_SUPPORT
/* Define if libcoap supports proxy code. */
#define COAP_PROXY_SUPPORT 1
#endif
#endif /* COAP_CLIENT_SUPPORT && COAP_SERVER_SUPPORT */

#ifndef COAP_WITH_OBSERVE_PERSIST
/* Define to build support for persisting observes. */
#define COAP_WITH_OBSERVE_PERSIST 0
#endif

#ifndef COAP_WS_SUPPORT
/* Define to 1 to build with WebSockets support. */
#define COAP_WS_SUPPORT 0
#endif

#ifndef COAP_Q_BLOCK_SUPPORT
/* Define to 1 to build with Q-Block (RFC9177) support. */
#define COAP_Q_BLOCK_SUPPORT 0
#endif

#ifndef COAP_MAX_LOGGING_LEVEL
/* Define to 0-8 for maximum logging level. */
#define COAP_MAX_LOGGING_LEVEL 8
#endif

#ifndef COAP_OSCORE_SUPPORT
/* Define to 1 to build with OSCORE support. */
#define COAP_OSCORE_SUPPORT 1
#endif

#ifndef COAP_ASYNC_SUPPORT
/* Define to 1 to build with support for async separate responses. */
#define COAP_ASYNC_SUPPORT 1
#endif

#ifndef COAP_THREAD_SAFE
/* Define to 1 if libcoap has thread safe support. */
#define COAP_THREAD_SAFE 0
#endif

#ifndef COAP_THREAD_RECURSIVE_CHECK
/* Define to 1 to build with thread recursive lock detection support. */
#define COAP_THREAD_RECURSIVE_CHECK 0
#endif

#ifndef PACKAGE_BUGREPORT
/* Define to the address where bug reports for this package should be sent. */
#define PACKAGE_BUGREPORT "<EMAIL>"
#endif /* PACKAGE_BUGREPORT */

#ifndef PACKAGE_NAME
/* Define to the full name of this package. */
#define PACKAGE_NAME "libcoap"
#endif /* PACKAGE_NAME */

#ifndef PACKAGE_STRING
/* Define to the full name and version of this package. */
#define PACKAGE_STRING "libcoap 4.3.5"
#endif /* PACKAGE_STRING */

#ifndef PACKAGE_TARNAME
/* Define to the one symbol short name of this package. */
#define PACKAGE_TARNAME "libcoap"
#endif /* PACKAGE_TARNAME */

#ifndef PACKAGE_URL
/* Define to the home page for this package. */
#define PACKAGE_URL "https://libcoap.net/"
#endif /* PACKAGE_URL */

#ifndef PACKAGE_VERSION
/* Define to the version of this package. */
#define PACKAGE_VERSION "4.3.5"
#endif /* PACKAGE_VERSION */

/* Define to 1 if you have the ANSI C header files. */
#define STDC_HEADERS 1

/* Define WORDS_BIGENDIAN to 1 if your processor stores words with the most
   significant byte first (like Motorola and SPARC, unlike Intel). */
#if defined AC_APPLE_UNIVERSAL_BUILD
# if defined __BIG_ENDIAN__
#  define WORDS_BIGENDIAN 1
# endif
#else
# ifndef WORDS_BIGENDIAN
/* #  undef WORDS_BIGENDIAN */
# endif
#endif

/* Define this to 1 for ancillary data on MacOS */
/* #undef __APPLE_USE_RFC_3542 */

/* Define to `unsigned int' if <sys/types.h> does not define. */
/* #undef size_t */

/* Define to `int' if <sys/types.h> does not define. */
/* #undef ssize_t */

#endif

#endif /* COAP_CONFIG_H_ */
