For Windows builds - see the Windows Section

Obtaining the Libcoap Source
============================

To get the libcoap library source, you need to do either the following

* Obtain the latest distribution package file from
  https://github.com/obgm/libcoap/archive/develop.zip
  [There is a stable version at
   https://github.com/obgm/libcoap/archive/main.zip]
* Change to the directory that you want to install the libcoap sub-directory
  into
* Unpack the distribution package file
* Change into the top level directory of the unpackaged files

or alternatively, clone the libcoap git repository from github

* Change to the directory that you want to install the libcoap sub-directory
  into.
* Then clone the latest (develop) version of the code:-
   git clone https://github.com/obgm/libcoap.git
* Change into the top level directory of the cloned files
* Optionally, change the branch from develop to the stable main branch:-
   git checkout main

Building Libcoap Libraries and Examples
=======================================

Follow the appropriate sections below


TinyDTLS Only
=============

It is possible that you may need to execute the following two commands once to
get the TinyDTLS code into your project, so the TinyDTLS library can be used.

 git submodule init
 git submodule update

General Building with cmake for linux/windows/macos/android (not for RIOT, LwIP or Contiki-NG - see below)
================

 cmake -E remove_directory build
 cmake -E make_directory build
 cd build
 cmake .. -DENABLE_TESTS=ON
 cmake --build .
 [sudo] cmake --build . -- install
 cd ..

 Note: to see possible options (TLS lib, doc, tests, examples etc.):
 cmake -LH build

 Note: For Windows, this is supported by Visual Studio Code with CMake extension
 Note: You must use cmake version >=3.10.

 Note: you can use cmake's find package after installation: find_package(libcoap-3 REQUIRED),
 and target_link_libraries(myTarget PRIVATE libcoap::coap-2)

 Note: Shared Library support is not currently available for Windows.

General Building with autoconf (not for RIOT, LwIP or Contiki-NG - see below)
================

 ./autogen.sh
 ./configure
 make
 sudo make install

./autogen.sh will fail if there is a required package for buildling libcoap
that is missing. Install the missing package and try ./autogen.sh again.

It is possible that you may need to provide some options to ./configure
to customize your installation.

In particular you may need to define which (D)TLS library to use as well as
disable some building of documentation.

General configure instructions can be found in INSTALL, which is built
by ./autogen.sh

 ./configure --help
gives the specific options available to libcoap.

Some examples are:-

# No DTLS
 ./configure --enable-tests --disable-documentation --enable-examples --disable-dtls --enable-shared

# With TinyDTLS
 ./configure --enable-tests --disable-documentation --enable-examples --with-tinydtls --enable-shared

Note: FreeBSD requires gmake instead of make when building TinyDTLS - i.e.
 gmake
 sudo gmake install

# With OpenSSL
 ./configure --with-openssl --enable-tests --enable-shared

# With wolfSSL
 ./configure --with-wolfssl --enable-tests --enable-shared

# With GnuTLS
 ./configure --with-gnutls --enable-tests --enable-shared

Note: --disable-documentation disables the building of doxygen and man page
files.  If you want to only disable one of them, use --disable-doxygen or
--disable-manpages.  Doxygen requires the program doxygen and man pages require
the program a2x to build the appropriate files.

If you need to rebuild the libcoap-*.{map,sym} files to update any exposed
function changes, run

 make update-map-file

prior to running 'make'.

RIOT
====

 cd examples/riot
 make

See examples/riot/README for further information.

LwIP
====

 cd examples/lwip
 make

Executable is ./server. See examples/lwip/README for further information.

Contiki-NG
==========

 cd examples/contiki
 make

Executable is ./server.native. See examples/contiki/README for further
information.

Windows
=======

Install OpenSSL (minimum version 1.1.0) including the development libraries if
not already installed.

Within Visual Studio, "Clone or check out code" using the repository
https://github.com/obgm/libcoap.git

You may need to update the SDK version of the libcoap Windows Project files to
match that of the SDK version of the Visual Studio you are using.  In Solution
Explorer with the view set to libcoap.sln, right click "Solution 'libcoap'"
and then "Retarget solution".

You may need to edit win32\libcoap.props to update the OpenSSLRootDir and
OpenSSLRootDirDbg variables to point to the top level directory where OpenSSL
is installed so that the include, lib etc. directories are correctly set up.
Note: Make sure that you include a trailing \ in the variable definitions.

Alternatively you can build everything in Visual Studio with CMake.

MinGW
=====

As there are many ways to install MinGW, depending on the different
installed packages, random failures can occur (usually because of conflicts
with cygwin based packages).  Below is a way known to work with libcoap
on a Windows host.

Remove any old copy of MSYS2 using Windows program remove.

Download https://repo.msys2.org/distrib/x86_64/msys2-x86_64-20230318.exe and
run the executable, accepting the defaults.

In a UCRT64 window, add in the following packages

 pacman -S git
 pacman -S vim
 pacman -S mingw-w64-ucrt-x86_64-cmake
 pacman -S mingw-w64-ucrt-x86_64-gcc
 pacman -S mingw-w64-ucrt-x86_64-openssl

Alternatively, in a MINGW64 window, add in the following packages

 pacman -S git
 pacman -S vim
 pacman -S mingw-w64-x86_64-cmake
 pacman -S mingw-w64-x86_64-gcc
 pacman -S mingw-w64-x86_64-openssl

Then clone a copy of the github libcoap repository in a UCRT64 or MINGW64 window

 git clone https://github.com/obgm/libcoap.git
 cd libcoap

Then build the libcoap library and example executables (which will be in the
build directory)

 cmake -E remove_directory build
 cmake -E make_directory build
 cd build
 cmake .. -DENABLE_DOCS=OFF -DDTLS_BACKEND=openssl
 cmake --build .
