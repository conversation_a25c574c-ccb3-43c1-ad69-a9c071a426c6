# Name,   Type, SubType, Offset,  Size, Flags
# Note: if you have increased the bootloader size, make sure to update the offsets to avoid overlap
esp_secure_cert,  0x3F,          ,    0xD000,     0x2000,    encrypted
nvs,        data, nvs,      , 0x6000,
storage,    data, nvs,      , 0x10000,    encrypted
otadata,    data, ota,      , 0x2000,
phy_init,   data, phy,      , 0x1000,
ota_0,      app,  ota_0,    , 2500K,
ota_1,      app,  ota_1,    , 2500K,
rcp_fw,     data, spiffs,   , 640K,
spiffs_storage,  data, spiffs,  , 0x10000,