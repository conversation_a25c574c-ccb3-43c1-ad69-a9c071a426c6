/* Standard includes. */
#include <string.h>
#include <math.h>
#include <inttypes.h>

/* Time includes. */
#include <time.h>

/* Socket includes. */
#include <sys/socket.h>

/* FreeRTOS includes. */
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "freertos/queue.h"

/* ESP-IDF includes. */
#include "esp_log.h"
#include "esp_wifi.h"
#include "esp_event.h"
#include "nvs_flash.h"
#include "protocol_examples_common.h"

/* Openthread */
#include "esp_openthread.h"
#include "openthread/thread.h"
#include "openthread/ip6.h"
#include "openthread/message.h"
#include "openthread/link.h"

/* CoAP includes. */
#include "coap3/coap.h"

/* Ledc includes. */
#include "app_manager.h"
#include "mqtt_manager.h"

/* MbedTLS includes. */
#include "mbedtls/sha256.h"

/* Custom Application Includes. */
#include "map.h"

#define VALUE_SIZE 4 // Only 4 of the 8 data are valid (greater than 0)
#define GET_RSSI_IN_OLD_VERSION 64
#define GET_RSSI_IN_NEW_VERSION 70

const static char *TAG = "CoAP_server";

float sen = 0.0;

// The last time the sensor connected to the gateway
int last_timestamp = 0;

/* Calculate the current related constants */
#define REFERENCE_VOLTAGE_V 3.3 // ADC internal voltage
#define ADC_RESOLUTION 12
#define ADC_MAX_VALUE ((1 << ADC_RESOLUTION) - 1) // 4095
#define STEP_SIZE (REFERENCE_VOLTAGE_V / ADC_MAX_VALUE)

// Add after the definition of STEP_SIZE

// Define shunt resistor values for different sensor types
#define SHUNT_RESISTOR_OHM_DEFAULT 0.0127   // Default shunt resistor
#define SHUNT_RESISTOR_OHM_TYPE1   0.0020   // HYP225
#define SHUNT_RESISTOR_OHM_TYPE2   0.0500   // HYPX
#define SHUNT_RESISTOR_OHM_TYPE3   0.0127   // HYP60v2
#define SHUNT_RESISTOR_OHM_TYPE4   0.0010   // HYP225v2
#define SHUNT_RESISTOR_OHM_TYPE5   0.0083   // HYP225v3
#define SHUNT_RESISTOR_OHM_TYPE6   0.0127   // HYP60v3
#define SHUNT_RESISTOR_OHM_TYPE7   0.0127   // HYP60v4

static inline float hypx_poly_current(uint32_t microVolts);
static inline float hyp225v3_poly_current(uint32_t microVolts);
/**
 * @brief Calculates current value in amperes from microvolt readings
 *
 * This function converts microvolt readings to amperes based on the sensor's 
 * shunt resistor value.
 *
 * @param microVolts Microvolt value from the sensor
 * @param sensorType Type of the sensor to determine shunt resistor value
 * @return Current value in amperes
 */
float calculateCurrentFromMicroVolts(uint32_t microVolts, uint8_t sensorType) {
    // Convert microvolts to volts
    float volts = microVolts / 1000000.0f;
    
    // Select appropriate shunt resistor value based on sensor type
    float shuntResistor = 0.0;
    switch (sensorType) {
        case 1:  shuntResistor = SHUNT_RESISTOR_OHM_TYPE1; break; // HYP225
        case 2:  // HYPX
            return hypx_poly_current(microVolts);
        case 3:  shuntResistor = SHUNT_RESISTOR_OHM_TYPE3; break; // HYP60v2
        case 4:  shuntResistor = SHUNT_RESISTOR_OHM_TYPE4; break; // HYP225v2
        case 5:  // HYP225v3
            return hyp225v3_poly_current(microVolts);
        case 6:  shuntResistor = SHUNT_RESISTOR_OHM_TYPE6; break; // HYP60v3
        case 7:  shuntResistor = SHUNT_RESISTOR_OHM_TYPE7; break; // HYP60v4
        default: shuntResistor = SHUNT_RESISTOR_OHM_DEFAULT;      // Default
    }
    
    // Calculate current using Ohm's law: I = V/R
    return volts / shuntResistor;
}

uint8_t sensorType = 0;

static uint8_t firmware_hash[32];
static int firmware_transfer_completed = 0;

// New file-scope static variables for OTA state management
static time_t last_ota_block_request_time = 0;
static mbedtls_sha256_context sha256_ctx_global;
static bool hash_initialized_global = false;
static bool hash_finished_global = false;

extern const unsigned char _binary_HYP_60_bin_start[];
extern const unsigned char _binary_HYP_60_bin_end[];

bool otaing = false;

/**
 * @brief Custom log handler for CoAP messages.
 *
 * This function processes CoAP log messages and routes them to the ESP-IDF logging system.
 * It splits multi-line messages at newline characters (`\n`) and logs each line individually
 * at the `ESP_LOG_INFO` level.
 *
 * @param level The CoAP log level (unused here, but available for future log-level mapping).
 * @param message Pointer to the CoAP log message string.
 */
static void coap_log_handler(coap_log_t level, const char *message)
{
    uint32_t esp_level = ESP_LOG_INFO;
    const char *cp = strchr(message, '\n');

    while (cp)
    {
        ESP_LOG_LEVEL(esp_level, TAG, "%.*s", (int)(cp - message), message);
        message = cp + 1;
        cp = strchr(message, '\n');
    }
    if (message[0] != '\000')
    {
        ESP_LOG_LEVEL(esp_level, TAG, "%s", message);
    }
}

typedef enum {
    ID_ASCII8,   // 8 字节 A-Z / 0-9
    ID_HEX16,    // 8 字节裸 MAC → 16 字符十六进制
    ID_INVALID
} id_fmt_t;

/**
 * 解析传感器发来的 8 字节 ID。
 *
 * @param buf      整包数据
 * @param len      数据长度
 * @param cursor   解析进度指针（会自动前移 8 字节）
 * @param id_out   输出字符串缓存（>=17 字节即可）
 * @return         ID_ASCII8 / ID_HEX16 / ID_INVALID
 */
static id_fmt_t parse_sensor_id(const uint8_t *buf,
                                size_t         len,
                                size_t        *cursor,
                                char          *id_out)
{
    /* 1. 边界检查 */
    if (*cursor + 8 > len) {
        return ID_INVALID;
    }

    const uint8_t *p = &buf[*cursor];

    /* 2. **首字节为 0x00 → 直接认定为 16 字节十六进制 MAC** */
    if (p[0] == 0x00) {
        for (int i = 0; i < 8; i++) {
            sprintf(&id_out[i * 2], "%02X", p[i]);
        }
        id_out[16] = '\0';
        *cursor   += 8;
        return ID_HEX16;
    }

    /* 3. 判定是否全部是可打印的 A-Z / 0-9（ASCII ID） */
    bool looks_ascii = true;
    for (int i = 0; i < 8; i++) {
        uint8_t ch = p[i];
        if (!((ch >= '0' && ch <= '9') || (ch >= 'A' && ch <= 'Z'))) {
            looks_ascii = false;
            break;
        }
    }

    if (looks_ascii) {
        memcpy(id_out, p, 8);
        id_out[8] = '\0';
        *cursor  += 8;
        return ID_ASCII8;
    }

    /* 4. 既不是 0x00 开头、也不是纯可打印字符 → 按协议视为无效 */
    return ID_INVALID;
}

/**
 * @brief Converts a binary data buffer into structured sensor data and extracts the MAC address.
 *
 * This function processes a binary buffer containing sensor data. It extracts the MAC address,
 * validates it, and populates a 2D array with the sensor data. Additionally, it updates the
 * power status for the corresponding MAC address.
 *
 * @param buffer Pointer to the input binary data buffer.
 * @param data 2D array [NUM_READINGS][VALUE_SIZE] to store the extracted sensor data.
 * @param mac Pointer to a character buffer to store the extracted MAC address in string format.
 * @return true if the conversion was successful, false if the MAC address is invalid.
 */
bool convert_from_binary(const uint8_t *buffer, size_t rxLen, int16_t data[NUM_READINGS][VALUE_SIZE], char *mac)
{
    size_t index = 0;
    size_t  idx      = 0;

    parse_sensor_id(buffer, rxLen, &idx, mac);

    index += 8;

    if (mac[0] == '0' && mac[1] == '0' && mac[2] == '0' && mac[3] == '0' && mac[4] == '0' && mac[5] == '0' && mac[6] == '0' && mac[7] == '0')
        return false;

    for (int i = 0; i < NUM_READINGS; i++)
    {
        for (int j = 0; j < VALUE_SIZE; j++)
        {
            // Combine MSB and LSB
            data[i][j] = (buffer[index] << 8) | buffer[index + 1];
            index += 2;
        }
    }

    int16_t isPowered = (buffer[index] << 8) | buffer[index + 1];
    addOrUpdateisPowered(mac, isPowered);

    index += 2;
    sensorType = (buffer[index] << 8) | buffer[index + 1];
    //printf("%u\n", sensorType);
    addOrUpdateType(mac, sensorType);

    index += 2;
    uint8_t firmware_major = (buffer[index] << 8) | buffer[index + 1];      
    index += 2;
    uint8_t firmware_minor = (buffer[index] << 8) | buffer[index + 1];   
    index += 2;
    uint8_t firmware_patch = (buffer[index] << 8) | buffer[index + 1];   

    addOrUpdateFirmwareVersion(mac, firmware_major, firmware_minor, firmware_patch);

    if (rxLen > 114) {
        addOrUpdateLowCurrent(mac, (bool)buffer[114]);
    } else {
        addOrUpdateLowCurrent(mac, false);
    }

    return true;
}

/**
 * @brief Converts a binary data buffer (new 64-byte format) into structured sensor data and extracts the MAC address.
 *
 * This function processes a binary buffer containing sensor data in the new 64-byte format.
 * It extracts the MAC address, validates it, and stores the 12 microvolt values.
 *
 * @param buffer Pointer to the input binary data buffer.
 * @param rxLen Length of the input data.
 * @param data Array to store the extracted microvolt values (1D array of size 12).
 * @param mac Pointer to a character buffer to store the extracted MAC address in string format.
 * @return true if the conversion was successful, false if the MAC address is invalid.
 */
bool convert_from_binary_new(const uint8_t *buffer, size_t rxLen, uint32_t *data, char *mac)
{
    size_t idx = 0;

    // Extract MAC address using existing parse_sensor_id function
    parse_sensor_id(buffer, rxLen, &idx, mac);

    if (mac[0] == '0' && mac[1] == '0' && mac[2] == '0' && mac[3] == '0' && 
        mac[4] == '0' && mac[5] == '0' && mac[6] == '0' && mac[7] == '0')
        return false;

    // Initialize data array with zeros
    for (int i = 0; i < 12; i++) {
        data[i] = 0;
    }

    // Extract all 12 microvolt values (each 4 bytes)
    // Starting at position 8 (after MAC)
    for (int i = 0; i < 12; i++) {
        // Calculate position for each 4-byte value
        size_t pos = 8 + (i * 4);
        
        // Ensure we don't exceed buffer boundaries
        if (pos + 3 < rxLen) {
            data[i] = (buffer[pos] << 24) | (buffer[pos+1] << 16) | 
                      (buffer[pos+2] << 8) | buffer[pos+3];
        }
    }
    
    // Process control bytes - positions based on currentsensor.c
    // isPowered at position 56
    uint8_t isPowered = buffer[56];
    addOrUpdateisPowered(mac, isPowered);
    
    // sensorType at position 57
    sensorType = buffer[57];
    addOrUpdateType(mac, sensorType);
    
    // Firmware version at positions 58, 59, 60
    uint8_t firmware_major = buffer[58];
    uint8_t firmware_minor = buffer[59];
    uint8_t firmware_patch = buffer[60];
    addOrUpdateFirmwareVersion(mac, firmware_major, firmware_minor, firmware_patch);
    
    // Low current flag at position 61
    if (rxLen > 61) {
        addOrUpdateLowCurrent(mac, (bool)buffer[61]);
    } else {
        addOrUpdateLowCurrent(mac, false);
    }

    // 处理新增的RSSI字段（位置62）
    if (rxLen >= 70) {
        int8_t rssi = (int8_t)buffer[62];
        addOrUpdateRssi(mac, rssi);
    }
    
    // 处理新增的message_id字段（位置63-66）
    if (rxLen >= 70) {
        uint32_t message_id = (buffer[63] << 24) | (buffer[64] << 16) | 
                             (buffer[65] << 8) | buffer[66];
        addOrUpdateMessageId(mac, message_id);
    }

    return true;
}

/**
 * @brief Performs sensitivity tuning based on the maximum value of current sensor data.
 *
 * Adjusts the sensitivity parameter dynamically based on the detected maximum value.
 *
 * @param max_value The maximum value detected in the current data samples.
 * @return The adjusted sensitivity value.
 */
static float hyp60_magnet_tuning(int16_t max_value)
{
    float sensitivity = 0.0;

    if(max_value < 20){ // 0.5A
        sensitivity = 78;
    }else if(max_value <= 40){ // 1A
        sensitivity = 62;
    }else if(max_value <=60){ // 1.5A
        sensitivity = 50;
    }else if(max_value < 80){ // 2A
        sensitivity = 50;
    }else if(max_value < 105) { // 2.5A
        sensitivity = 49;
    }else if(max_value < 120) { // 3A
        sensitivity = 48;
    }else if (max_value < 145) { // 3.5A
        sensitivity = 47;
    }else if(max_value < 165) { // 4A
        sensitivity = 47;
    }else if(max_value < 185) { // 4.5A
        sensitivity = 47;
    }else if(max_value < 205) { // 5A
        sensitivity = 46;
    }else if(max_value < 240) { // 6A
        sensitivity = 46;
    }else if(max_value < 285) { // 7A
        sensitivity = 46;
    }else if(max_value < 475) { // 12A
        sensitivity = 45;
    }else if(max_value < 530) { // 15A
        sensitivity = 47;
    }else if(max_value < 570) { // 20A
        sensitivity = 55;
    }else if(max_value < 575) { // 25A
        sensitivity = 64.5;
    }else {
        sensitivity = 73;
    }

    // printf("sen:%f\n",sen);
    // sensitivity = sen;

    return sensitivity;
}

/**
 * @brief Performs sensitivity tuning based on the maximum value of current sensor data.
 *
 * Adjusts the sensitivity parameter dynamically based on the detected maximum value.
 *
 * @param max_value The maximum value detected in the current data samples.
 * @return The adjusted sensitivity value.
 */
static float hyp60v2_magnet_tuning(int16_t max_value)
{
    float sensitivity = 0.0;

    if(max_value < 25){ // 0.5A
        sensitivity = 60;
    }else if(max_value <= 45){ // 1A
        sensitivity = 50;
    }else if(max_value <=85){ // 2A
        sensitivity = 48;
    }else if(max_value < 120){ // 3A
        sensitivity = 47;
    }else if(max_value < 170) { // 4A
        sensitivity = 47;
    }else if(max_value < 215) { // 5A
        sensitivity = 47;
    }else if (max_value < 295) { // 7A
        sensitivity = 46;
    }else if(max_value < 425) { // 10A
        sensitivity = 45.8;
    }else if(max_value < 640) { // 15A
        sensitivity = 45.5;
    }else if(max_value < 830) { // 20A
        sensitivity = 45.5;
    }else if(max_value < 1040) { // 25A
        sensitivity = 45.5;
    }else if(max_value < 1250) { // 30A
        sensitivity = 45;
    }else if(max_value < 1450) { // 35A
        sensitivity = 45.5;
    }else if(max_value < 1650) { // 40A
        sensitivity = 45.5;
    }else if(max_value < 1850) { // 45A
        sensitivity = 45.3;
    }else if(max_value < 2050) { // 50A
        sensitivity = 45.3;
    }else if(max_value < 2250) { // 55A
        sensitivity = 45.3;
    }else {                     // 60A
        sensitivity = 45;
    }
    // printf("sen:%f\n",sen);
    // sensitivity = sen;

    return sensitivity;
}

/**
 * @brief Performs sensitivity tuning based on the maximum value of current sensor data.
 *
 * Adjusts the sensitivity parameter dynamically based on the detected maximum value.
 *
 * @param max_value The maximum value detected in the current data samples.
 * @return The adjusted sensitivity value.
 */
static float hyp60v3_magnet_tuning(int16_t max_value)
{
    float sensitivity = 0.0;

    if(max_value < 25){ // 0.5A
        sensitivity = 60;
    }else if(max_value <= 45){ // 1A
        sensitivity = 50;
    }else if(max_value <=85){ // 2A
        sensitivity = 48;
    }else if(max_value < 120){ // 3A
        sensitivity = 47;
    }else if(max_value < 170) { // 4A
        sensitivity = 45;
    }else if(max_value < 215) { // 5A
        sensitivity = 45;
    }else if (max_value < 295) { // 7A
        sensitivity = 45;
    }else if(max_value < 425) { // 10A
        sensitivity = 44;
    }else if(max_value < 640) { // 15A
        sensitivity = 44;
    }else if(max_value < 830) { // 20A
        sensitivity = 44;
    }else if(max_value < 1040) { // 25A
        sensitivity = 44;
    }else if(max_value < 1250) { // 30A
        sensitivity = 44;
    }else if(max_value < 1450) { // 35A
        sensitivity = 44;
    }else if(max_value < 1650) { // 40A
        sensitivity = 44.5;
    }else if(max_value < 1850) { // 45A
        sensitivity = 44.5;
    }else if(max_value < 2050) { // 50A
        sensitivity = 44.5;
    }else if(max_value < 2250) { // 55A
        sensitivity = 44.5;
    }else {                     // 60A
        sensitivity = 44.7;
    }
    // printf("sen:%f\n",sen);
    // sensitivity = sen;

    return sensitivity;
}

/**
 * @brief Performs sensitivity tuning based on the maximum value of current sensor data.
 *
 * Adjusts the sensitivity parameter dynamically based on the detected maximum value.
 *
 * @param max_value The maximum value detected in the current data samples.
 * @return The adjusted sensitivity value.
 */
static float hyp60v4_magnet_tuning(int16_t max_value)
{
    float sensitivity = 0.0;

    if(max_value < 25){ // 0.5A
        sensitivity = 46;
    }else if(max_value <=95){ // 2A
        sensitivity = 44;
    }else if(max_value < 150){ // 3A
        sensitivity = 42;
    }else if(max_value < 200) { // 4A
        sensitivity = 42;
    }else if(max_value < 250) { // 5A
        sensitivity = 40;
    }else if (max_value < 350) { // 7A
        sensitivity = 40;
    }else if(max_value < 460) { // 10A
        sensitivity = 40;
    }else if(max_value < 650) { // 15A
        sensitivity = 42;
    }else if(max_value < 915) { // 20A
        sensitivity = 42;
    }else if(max_value < 1150) { // 25A
        sensitivity = 43;
    }else if(max_value < 1325) { // 30A
        sensitivity = 44;
    }else if(max_value < 1530) { // 35A
        sensitivity = 44;
    }else if(max_value < 1705) { // 40A
        sensitivity = 44;
    }else if(max_value < 1940) { // 45A
        sensitivity = 44;
    }else if(max_value < 2150) { // 50A
        sensitivity = 44;
    }else if(max_value < 2350) { // 55A
        sensitivity = 44;
    }else {                     // 60A
        sensitivity = 43;
    }
    // printf("sen:%f\n",sen);
    // sensitivity = sen;

    return sensitivity;
}

static float hyp225_magnet_tuning(int16_t max_value)
{
    float sensitivity = 0.0;

    if(max_value < 180){ // 80A
        sensitivity = 800;
    }else if(max_value <= 240){ // 100A
        sensitivity = 780;
    }else if(max_value <=350){ // 120A
        sensitivity = 740;
    }else { // 150A
        sensitivity = 700;
    }

    // printf("sen:%f\n",sen);
    // sensitivity = sen;

    return sensitivity;
}

static float hyp225v2_magnet_tuning(int16_t max_value)
{
    float sensitivity = 0.0;

    if(max_value < 160){ // 10A
        sensitivity = 153;
    }else if(max_value <= 310){ // 20A
        sensitivity = 146;
    }else if(max_value <=440){ // 30A
        sensitivity = 147;
    }else if(max_value <=575){ // 40A
        sensitivity = 144;
    }else if(max_value <=705){ // 50A
        sensitivity = 143;
    }else if(max_value <=1500){ // 150A
        sensitivity = 150;
    }else { // 225a
        sensitivity = 170;
    }

    // printf("sen:%f\n",sen);
    // sensitivity = sen;

    return sensitivity;
}

static inline float hyp225v3_poly_current(uint32_t microVolts) {
    /* 先把微伏缩放到 0-1 数量级，避免高次项溢出 */
    const double x = microVolts * 1e-6;     // 46926 µV → 0.046926

    /* 9th-order coefficients (重新标定所得) */
    const double a9 =  8.55625315e+05;
    const double a8 = -3.08898916e+06;
    const double a7 =  4.72003998e+06;
    const double a6 = -3.97855272e+06;
    const double a5 =  2.02041066e+06;
    const double a4 = -6.32564590e+05;
    const double a3 =  1.19128177e+05;
    const double a2 = -1.22209109e+04;
    const double a1 =  7.44855943e+02;
    const double a0 = -1.23369342e+01;

    /* 霍纳法则求值：(((((((((a9*x + a8)*x + a7)...)+a1)*x)+a0 */
    double currentA = ((((((((a9 * x + a8) * x + a7) * x + a6) * x + a5)
                        * x + a4) * x + a3) * x + a2) * x + a1) * x + a0;
    if (currentA < 0.0)
        currentA = 0.0;
    return (float)currentA;
}

static float hyp225v3_magnet_tuning(int16_t max_value)
{
    float sensitivity = 0.0;

    if(max_value < 145){ // 7A
        sensitivity = 80;
    }else if(max_value <= 230){ // 11A
        sensitivity = 80;
    }else if(max_value <=345){ // 20A
        sensitivity = 93;
    }else if(max_value <=470){ // 30A
        sensitivity = 106;
    }else if(max_value <=575){ // 40A
        sensitivity = 115;
    }else if(max_value <=690){ // 50A
        sensitivity = 123;
    }else if(max_value <=800){ // 60A
        sensitivity = 128;
    }else if(max_value <=920){ // 70A
        sensitivity = 131;
    }else if(max_value <=1020){ // 80A
        sensitivity = 135;
    }else if(max_value <=1140){ // 90A
        sensitivity = 137;
    }else if(max_value <=1230){ // 100A
        sensitivity = 140;
    }else if(max_value <=1380){ // 110A
        sensitivity = 141;
    }else if(max_value <=1480){ // 120A
        sensitivity = 143;
    }else if(max_value <=1600){ // 130A
        sensitivity = 143;
    }else if(max_value <=1710){ // 140A
        sensitivity = 145;
    }else if(max_value <=1820){ // 150A
        sensitivity = 146;
    }else if(max_value <=1940){ // 160A
        sensitivity = 146;
    }else if(max_value <=2160){ // 180A
        sensitivity = 149;
    }else if(max_value <=2390){ // 200A
        sensitivity = 154;
    }else { // 220a
        sensitivity = 160;
    }

    //printf("sen:%f\n",sen);
    //sensitivity = sen;

    return sensitivity;
}

static inline float hypx_poly_current(uint32_t microVolts) {
    /* -------- 二次多项式系数 (double 精度) -------- */
    const double k2 =  1.93029585;   /* A / V² */
    const double k1 =  5.71896488;   /* A / V  */
    const double k0 =  0.04613699;   /* A      */

    /* µV → V，并用霍纳法则求值 */
    const double V = microVolts * 1e-6;
    double current = (k2 * V + k1) * V + k0;

    if (current < 0.0) current = 0.0;   /* 额外保护 */
    return (float)current;
}

static float hypx_magnet_tuning(int16_t max_value)
{
    float sensitivity = 4;

    // printf("sen:%f\n",sen);
    // sensitivity = sen;

    return sensitivity;
}

/**
 * @brief Calculates RMS current values from sampled sensor data.
 *
 * Processes a 2D array of sensor data, applies sensitivity tuning, and calculates the
 * root mean square (RMS) values for each channel.
 *
 * @param data 2D array of sampled current data [NUM_READINGS][VALUE_SIZE samples].
 * @param rms_values Pointer to an array to store the calculated RMS values.
 */
void calculate_current(int16_t data[NUM_READINGS][VALUE_SIZE], float *rms_values)
{
    // Iterate through each channel's data
    for (int i = 0; i < NUM_READINGS; i++)
    {
        double sum_of_squares = 0.0; // Stores the sum of squares for RMS calculation
        int16_t max_value = 0;

        // Find the maximum value in the current channel's data
        for (int j = 0; j < VALUE_SIZE; j++)
        {
            max_value = (max_value > data[i][j]) ? max_value : data[i][j];
        }

        // Perform sensitivity tuning using the extracted function
        float sensitivity = 0.0;
        if(sensorType==1) {
            sensitivity = hyp225_magnet_tuning(max_value);
        }else if(sensorType==2) {
            sensitivity = hypx_magnet_tuning(max_value);
        }else if(sensorType==3){
            sensitivity = hyp60v2_magnet_tuning(max_value);
        }else if(sensorType==4){
            sensitivity = hyp225v2_magnet_tuning(max_value);
        }else if(sensorType==5){
            sensitivity = hyp225v3_magnet_tuning(max_value);
        }else if(sensorType==6){
            sensitivity = hyp60v3_magnet_tuning(max_value);
        }else if(sensorType==7){
            sensitivity = hyp60v4_magnet_tuning(max_value);
        }else{
            sensitivity = hyp60_magnet_tuning(max_value);
        }
        
        // Process each sample in the channel
        for (int j = 0; j < VALUE_SIZE; j++)
        {
            if(data[i][j] <= 4) {
                continue;
            }
            if (data[i][j] > 4)
            {
                float voltage = (data[i][j] - 4) * STEP_SIZE;
                double current = voltage * sensitivity;
                sum_of_squares += current * current;
            }
        }

        // Calculate the RMS value
        double mean = (sum_of_squares / VALUE_SIZE);
        float rms = (float)sqrt(mean);

        rms_values[i] = rms;
    }
}

/**
 * @brief Calculates current value from sampled sensor data in the new format.
 *
 * Processes the sensor data from the new 64-byte format and calculates the
 * current value directly from the microvolt readings.
 *
 * @param data Array containing the microvolt values from the new format.
 * @param rms_values Pointer to an array to store the calculated current values.
 */
void calculate_current_new(uint32_t *data, float *rms_values)
{
    // Clear all values first
    for (int i = 0; i < NUM_READINGS; i++) {
        rms_values[i] = 0.0;
    }
    
    // Process each microvolt value and convert to amperes
    for (int i = 0; i < NUM_READINGS; i++) {
        uint32_t microvolts = data[i];
        
        // Calculate current directly from microvolts
        if (microvolts > 0) {
            rms_values[i] = calculateCurrentFromMicroVolts(microvolts, sensorType);
        }
    }
}

// Strip the port number from an IPv6 address string
void extract_ipv6_address(const char *input, char *output) {
    const char *start = strchr(input, '[');
    const char *end = strchr(input, ']');

    if (start && end && (end > start)) {
        strncpy(output, start + 1, end - start - 1);
        output[end - start - 1] = '\0';
    } else {
        // If the address does not have a port number, copy it directly
        strncpy(output, input, 46);
    }
}
void fix_u_l_bit_in_ipv6(uint8_t *ipv6_byte) {
    *ipv6_byte ^= 0x02;  // Flip U/L bit
}

void setChildRssiFromIpv6(coap_session_t *session, char *mac) {
    // Get an OpenThread instance
    otInstance *otInstance = esp_openthread_get_instance();
    if (!otInstance) {
        printf("Failed to get OpenThread instance\n");
        return;
    }

    // Get the sender address
    const coap_address_t *peer_address = coap_session_get_addr_remote(session);
    char full_addr_str[INET6_ADDRSTRLEN];
    char ipv6_addr_str[46];

    if (coap_print_addr(peer_address, (unsigned char *)full_addr_str, sizeof(full_addr_str)) > 0) {
        //printf("Received CoAP message from: %s\n", full_addr_str);
    } else {
        printf("Failed to get sender address\n");
        return;
    }

    // Extracting pure IPv6 addresses
    extract_ipv6_address(full_addr_str, ipv6_addr_str);

    // Convert an IPv6 address string to otIp6Address
    otIp6Address ipv6_address;
    if (otIp6AddressFromString(ipv6_addr_str, &ipv6_address) != OT_ERROR_NONE) {
        printf("Invalid IPv6 address format: %s\n", ipv6_addr_str);
        return;
    }

    // Corrected the U/L bit of the first byte of the IPv6 address to make it consistent with the MAC address
    fix_u_l_bit_in_ipv6(&ipv6_address.mFields.m8[8]);

    // Traverse the neighbor table to find a matching MAC address
    otNeighborInfoIterator iterator = OT_NEIGHBOR_INFO_ITERATOR_INIT;
    otNeighborInfo neighbor_info;

    bool found = false;
    while (otThreadGetNextNeighborInfo(otInstance, &iterator, &neighbor_info) == OT_ERROR_NONE) {
        // printf("Checking neighbor with MAC address: ");
        // for (int i = 0; i < OT_EXT_ADDRESS_SIZE; i++) {
        //     printf("%02X", neighbor_info.mExtAddress.m8[i]);
        // }
        // printf("\n");

        // // Print the last 64 bits of the IPv6 address and the MAC address from the neighbor table
        // printf("IPv6 address last 64 bits: ");
        // for (int i = 8; i < 16; i++) {
        //     printf("%02X", ipv6_address.mFields.m8[i]);
        // }
        // printf("\n");


        if (memcmp(&ipv6_address.mFields.m8[8], neighbor_info.mExtAddress.m8, 8) == 0) {
            // printf("Matched MAC address: ");
            // for (int i = 0; i < OT_EXT_ADDRESS_SIZE; i++) {
            //     printf("%02X", neighbor_info.mExtAddress.m8[i]);
            // }
            // printf("\n");
            // printf("RSSI: Avg = %d dBm, Last = %d dBm\n", neighbor_info.mAverageRssi, neighbor_info.mLastRssi);
            found = true;

            addOrUpdateRssi(mac, neighbor_info.mLastRssi);

            break;
        }
    }

    if (!found) {
        printf("No matching MAC address found in the neighbor table\n");
    }
}

/**
 * @brief Handles a POST request to process current sensor data.
 *
 * This function extracts binary data from a CoAP POST request, processes it to calculate RMS values,
 * updates timestamps, and updates the current sensor data. It also sends back the sleep time
 * for the requesting device as the response.
 *
 * @param resource Pointer to the CoAP resource associated with this handler.
 * @param session Pointer to the CoAP session handling the request.
 * @param request Pointer to the incoming CoAP request PDU.
 * @param query Pointer to the CoAP query string (optional).
 * @param response Pointer to the CoAP response PDU to be sent.
 */
static void hnd_current_sensor_post(coap_resource_t *resource,
                                    coap_session_t *session,
                                    const coap_pdu_t *request,
                                    const coap_string_t *query,
                                    coap_pdu_t *response)
{
    // Turn on the blue LED to indicate processing
    ledtask_operation(BLUE, OPEN);

    size_t size;
    const uint8_t *data;
    char addr_only[MAC_ADDR_STRLEN];

    // Extracting data from a POST request
    coap_get_data(request, &size, &data);

    float rms_values[NUM_READINGS] = {0};

    if (size != 0)
    {
        bool conversion_success = false;
        
        // Check data size to determine which format to process
        if (size == GET_RSSI_IN_NEW_VERSION) {
            // New 70-byte format with RSSI and message_id
            uint32_t new_format_data[12] = {0}; // 1D array for new format
            conversion_success = convert_from_binary_new(data, size, new_format_data, addr_only);
            if (conversion_success) {
                calculate_current_new(new_format_data, rms_values);
            }
        } else if (size == GET_RSSI_IN_OLD_VERSION) {
            // New 64-byte format, get RSSI from parent
            uint32_t new_format_data[12] = {0}; // 1D array for new format
            conversion_success = convert_from_binary_new(data, size, new_format_data, addr_only);
            if (conversion_success) {
                calculate_current_new(new_format_data, rms_values);
                setChildRssiFromIpv6(session, addr_only);
            }
        } else {
            // Legacy format (typically 115 bytes)
            int16_t value[NUM_READINGS][VALUE_SIZE];
            conversion_success = convert_from_binary(data, size, value, addr_only);
            if (conversion_success) {
                // For debugging
                // for (int i = 0; i < 12; i++) {
                //     for (int j = 0; j < VALUE_SIZE; j++) {
                //         printf("%d ", value[i][j]);
                //     }
                //     printf("\n");
                // }
                calculate_current(value, rms_values);
                // 旧格式也需要使用传统方法获取RSSI
                setChildRssiFromIpv6(session, addr_only);
            }
        }
        
        if (!conversion_success) {
            ESP_LOGW(TAG, "Invalid data received, conversion failed");
            return; // Invalid data, exit function
        }

        /* ----- 更新本地缓存数据 ----- */
        // 获取当前时间并格式化
        time_t now = 0;
        time(&now);
        int timestamp = (int)now;

        char strftime_buf[64];
        struct tm timeinfo = {0};
        setenv("TZ", "UTC", 1);
        tzset();
        localtime_r(&now, &timeinfo);
        strftime(strftime_buf, sizeof(strftime_buf), "%c", &timeinfo);
        last_timestamp = timestamp;

        // 先更新缓存，再发送队列（避免立刻被删除后又重新插入）
        addOrUpdateTimestamp(addr_only, strftime_buf);
        addOrUpdateCurrent(addr_only, rms_values);

        // 调试输出
        //displayTimestamps();
        //printCurrentVector();

        /* ----- 把 MAC 地址发送到发布任务队列 ----- */
        if (sensor_update_queue != NULL) {
            if (xQueueSend(sensor_update_queue, addr_only, portMAX_DELAY) != pdTRUE) {
                ESP_LOGE(TAG, "Failed to send sensor MAC to queue");
            }
        }
    }

    // Retrieve the sleep time for the device
    uint16_t response_value = getSleepTimeByMac(addr_only) - 1;

    otBufferInfo bufInfo;
    otMessageGetBufferInfo(esp_openthread_get_instance(), &bufInfo);

    //ESP_LOGI(TAG, "OT buffers: Free = %u / Total = %u / MaxUsed = %u", bufInfo.mFreeBuffers, bufInfo.mTotalBuffers, bufInfo.mMaxUsedBuffers);

    if (bufInfo.mFreeBuffers < 10) {
        ESP_LOGW(TAG, "Low buffer warning! Skipping response to avoid NoBufs");
        return;
    }
    coap_pdu_set_code(response, COAP_RESPONSE_CODE_CHANGED);
    coap_add_data(response, sizeof(response_value), (const uint8_t *)&response_value);
}

/**
 * @brief Handles a GET request to check the firmware version of the client.
 *
 * This function processes a CoAP GET request that includes a query parameter specifying
 * the client's firmware version. It compares the client's version with the current firmware
 * version defined in `CONFIG_FIRMWARE_VERSION` and responds accordingly.
 *
 * @param resource Pointer to the CoAP resource associated with this handler.
 * @param session Pointer to the CoAP session handling the request.
 * @param request Pointer to the incoming CoAP request PDU.
 * @param query Pointer to the CoAP query string containing the firmware version.
 * @param response Pointer to the CoAP response PDU to be sent.
 */
static void hnd_version_get(coap_resource_t *resource,
                            coap_session_t *session,
                            const coap_pdu_t *request,
                            const coap_string_t *query,
                            coap_pdu_t *response)
{
    char client_version[64] = {0};

    // Parse query parameters to extract the client's firmware version
    if (query && query->s && query->length < sizeof(client_version))
    {
        strncpy(client_version, (char *)query->s, query->length);
        client_version[query->length] = '\0';

        ESP_LOGI(TAG, "Received firmware version: %s", client_version);
        char *version_prefix = "version=";
        if (strncmp(client_version, version_prefix, strlen(version_prefix)) == 0)
        {
            char *version = client_version + strlen(version_prefix);

            if (strcmp(version, CONFIG_FIRMWARE_VERSION) == 0)
            {
                const char *response_message = "No update needed";
                coap_pdu_set_code(response, COAP_RESPONSE_CODE_CONTENT);
                coap_add_data(response, strlen(response_message), (const uint8_t *)response_message);
                ESP_LOGI(TAG, "Client firmware is up-to-date.");
                return;
            }
            else if(otaing){
                const char *response_message = "OTA busy";
                coap_pdu_set_code(response, COAP_RESPONSE_CODE_CONTENT);
                coap_add_data(response, strlen(response_message), (const uint8_t *)response_message);
                ESP_LOGI(TAG, "OTA is busy");
                return;
            }
            else
            {
                const char *response_message = "Update available";
                coap_pdu_set_code(response, COAP_RESPONSE_CODE_CONTENT);
                coap_add_data(response, strlen(response_message), (const uint8_t *)response_message);
                ESP_LOGI(TAG, "Client firmware is outdated. Update available.");
                return;
            }
        }
    }

    // If the query is invalid, respond with an error message
    const char *error_message = "Invalid request";
    coap_pdu_set_code(response, COAP_RESPONSE_CODE_BAD_REQUEST);
    coap_add_data(response, strlen(error_message), (const uint8_t *)error_message);
    ESP_LOGW(TAG, "Invalid firmware version query.");
}

/**
 * @brief Handles a GET request to retrieve the firmware file in blocks using CoAP.
 *
 * This function supports block-wise transfer for a firmware file. It reads the file in chunks
 * and sends each block to the client. The response includes Block2 option metadata for the
 * client to request subsequent blocks.
 *
 * @param resource Pointer to the CoAP resource associated with this handler.
 * @param session Pointer to the CoAP session handling the request.
 * @param request Pointer to the incoming CoAP request PDU.
 * @param query Pointer to the CoAP query string (optional).
 * @param response Pointer to the CoAP response PDU to be sent.
 */
static void hnd_firmware_get(coap_resource_t *resource,
                             coap_session_t *session,
                             const coap_pdu_t *request,
                             const coap_string_t *query,
                             coap_pdu_t *response)
{
    otaing = true;
    last_ota_block_request_time = time(NULL); // Add timestamp update
    static uint8_t buffer[1024 + 4];
    const unsigned char *hyp_60_bin = _binary_HYP_60_bin_start;
    size_t hyp_60_bin_len = _binary_HYP_60_bin_end - _binary_HYP_60_bin_start;

    // Initialize SHA-256 calculation using global variables
    if (!hash_initialized_global)
    {
        mbedtls_sha256_init(&sha256_ctx_global);
        mbedtls_sha256_starts(&sha256_ctx_global, 0); // 0 means calculation standard SHA-256
        hash_initialized_global = true;
        ESP_LOGI(TAG, "SHA256 context initialized for embedded firmware.");
        ESP_LOGI(TAG, "Embedded Firmware size: %u bytes", hyp_60_bin_len);
    }

    // Handle block-wise transfer
    coap_block_t block;
    if (!coap_get_block(request, COAP_OPTION_BLOCK2, &block))
    {
        block.num = 0;
        block.m = 1;
        block.szx = 6;
    }
    // Log the block number being requested
    ESP_LOGI(TAG, "request: block.num = %u", block.num);

    size_t real_block_size = 1024;
    size_t offset = block.num * real_block_size;

    if (offset >= hyp_60_bin_len) // Use embedded length
    {
        ESP_LOGW(TAG, "Block request exceeds firmware size (%u >= %u).", (unsigned int)offset, hyp_60_bin_len);
        coap_pdu_set_code(response, COAP_RESPONSE_CODE_BAD_REQUEST);
        // No cleanup needed here as no file is open
        return; // Return directly instead of goto cleanup for this specific error
    }

    size_t remaining = hyp_60_bin_len - offset; // Use embedded length
    size_t current_block_size = remaining > real_block_size ? real_block_size : remaining;

    // Read data directly from memory instead of file
    memcpy(buffer + 4, hyp_60_bin + offset, current_block_size);

    if (!hash_finished_global) // Use global flag
    {
        // Update SHA hash - Use the actual data copied
        mbedtls_sha256_update(&sha256_ctx_global, buffer + 4, current_block_size); // Use global context
    }

    ESP_LOGI(TAG, "offset: %u, current_block_size: %lu, offset + current_block_size: %lu, total_size: %u",
             (unsigned int)offset, (unsigned long)current_block_size, (unsigned long)(offset + current_block_size), hyp_60_bin_len); // Use embedded length
    block.m = (offset + current_block_size < hyp_60_bin_len); // Use embedded length

    // Fill the first 4 bytes of the buffer with Block2 metadata
    buffer[0] = (uint8_t)(block.num >> 8); // High byte of block number
    buffer[1] = (uint8_t)(block.num);      // Low byte of block number
    buffer[2] = block.m;                   // More blocks flag
    buffer[3] = block.szx;                 // Block size exponent

    coap_pdu_set_code(response, COAP_RESPONSE_CODE_CONTENT);

    // Add a short delay to allow OpenThread to process buffers
    vTaskDelay(pdMS_TO_TICKS(50)); // 50ms delay

    // Add the block data to the response
    coap_add_data(response, current_block_size + 4, buffer);

    ESP_LOGI(TAG, "Sent block %u, size %lu, more blocks: %d", block.num, (unsigned long)current_block_size, block.m);

    if (!block.m)
    {
        // Use the existing global flag to ensure hash finalization happens only once
        if(firmware_transfer_completed == 0){
            firmware_transfer_completed = 1;
            if (!hash_finished_global) // Use global flag
            {
                mbedtls_sha256_finish(&sha256_ctx_global, firmware_hash); // Use global context
                hash_finished_global = true; // Use global flag
                // Clean up SHA context as OTA is completed
                mbedtls_sha256_free(&sha256_ctx_global);
                hash_initialized_global = false; // Reset for next OTA
                hash_finished_global = false;    // Reset for next OTA
            }
            last_ota_block_request_time = 0; // OTA completed, reset timer
        }
        ESP_LOGI(TAG, "Firmware transfer completed using embedded data.");
    }
}

/**
 * @brief Handles a GET request to return the firmware hash.
 *
 * This function processes an incoming CoAP GET request and responds with the firmware hash value.
 *
 * @param resource Pointer to the CoAP resource associated with this handler.
 * @param session Pointer to the CoAP session handling the request.
 * @param request Pointer to the incoming CoAP request PDU.
 * @param query Pointer to the CoAP query string (optional).
 * @param response Pointer to the CoAP response PDU to be sent.
 */
static void hnd_firmware_hash_get(coap_resource_t *resource,
                                  coap_session_t *session,
                                  const coap_pdu_t *request,
                                  const coap_string_t *query,
                                  coap_pdu_t *response)
{
    // Set the response code to 2.05 (Content) to indicate a successful response.
    coap_pdu_set_code(response, COAP_RESPONSE_CODE_CONTENT);

    // Add the firmware hash value to the response payload.
    coap_add_data(response, sizeof(firmware_hash), firmware_hash);

    ESP_LOGI(TAG, "Sent firmware hash to device.");

    otaing = false;
    last_ota_block_request_time = 0; // Reset OTA timer when hash is fetched
}

void coap_server(void *p)
{

    coap_context_t *ctx = NULL;
    coap_resource_t *resource = NULL;
    int have_ep = 0;
    uint16_t u_s_port = atoi(CONFIG_EXAMPLE_COAP_LISTEN_PORT);
    uint16_t s_port = atoi(CONFIG_EXAMPLE_COAPS_LISTEN_PORT);

    uint16_t ws_port = 0;
    uint16_t ws_s_port = 0;
    uint32_t scheme_hint_bits;

    // 添加时间变量，用于定时执行丢包补偿检查
    time_t last_compensation_check_time = 0;
    const int COMPENSATION_CHECK_INTERVAL = 30; // 检查间隔30秒

    /* Initialize libcoap library */
    coap_startup();

    coap_set_log_handler(coap_log_handler);
    coap_set_log_level(CONFIG_COAP_LOG_DEFAULT_LEVEL);

    while (1)
    {
        unsigned wait_ms;
        coap_addr_info_t *info = NULL;
        coap_addr_info_t *info_list = NULL;

        ctx = coap_new_context(NULL);
        if (!ctx)
        {
            ESP_LOGE(TAG, "coap_new_context() failed");
            goto clean_up;
        }
        coap_context_set_block_mode(ctx,
                                    COAP_BLOCK_USE_LIBCOAP | COAP_BLOCK_SINGLE_BODY);
        coap_context_set_max_idle_sessions(ctx, 20);

        /* Need PSK setup before we set up endpoints */
        coap_context_set_psk(ctx, "CoAP",
                             (const uint8_t *)CONFIG_EXAMPLE_COAP_PSK_KEY,
                             sizeof(CONFIG_EXAMPLE_COAP_PSK_KEY) - 1);

        /* set up the CoAP server socket(s) */
        scheme_hint_bits =
            coap_get_available_scheme_hint_bits(1, 0, 0);

        info_list = coap_resolve_address_info(coap_make_str_const("::"), u_s_port, s_port,
                                              ws_port, ws_s_port,
                                              0,
                                              scheme_hint_bits,
                                              COAP_RESOLVE_TYPE_LOCAL);

        if (info_list == NULL)
        {
            ESP_LOGE(TAG, "coap_resolve_address_info() failed");
            goto clean_up;
        }

        for (info = info_list; info != NULL; info = info->next)
        {
            coap_endpoint_t *ep;

            ep = coap_new_endpoint(ctx, &info->addr, info->proto);
            if (!ep)
            {
                ESP_LOGW(TAG, "cannot create endpoint for proto %u", info->proto);
            }
            else
            {
                have_ep = 1;
            }
        }
        coap_free_address_info(info_list);
        if (!have_ep)
        {
            ESP_LOGE(TAG, "No endpoints available");
            goto clean_up;
        }

        // Initialize the resource for CurrentSensor/Currents
        resource = coap_resource_init(coap_make_str_const("CurrentSensor/Currents"), 0);
        if (!resource)
        {
            ESP_LOGE(TAG, "coap_resource_init() failed for CurrentSensor/Currents");
            goto clean_up;
        }
        coap_register_handler(resource, COAP_REQUEST_POST, hnd_current_sensor_post);
        coap_add_resource(ctx, resource);

        resource = coap_resource_init(coap_make_str_const("version"), 0);
        if (!resource)
        {
            ESP_LOGE(TAG, "coap_resource_init() failed for version");
            goto clean_up;
        }
        coap_register_handler(resource, COAP_REQUEST_GET, hnd_version_get);
        coap_add_resource(ctx, resource);

        resource = coap_resource_init(coap_make_str_const("firmware"), 0);
        if (!resource)
        {
            ESP_LOGE(TAG, "coap_resource_init() failed for firmware");
            goto clean_up;
        }
        coap_register_handler(resource, COAP_REQUEST_GET, hnd_firmware_get);
        coap_add_resource(ctx, resource);

        // 初始化用于返回固件哈希的资源，URI 路径为 "firmware/hash"
        resource = coap_resource_init(coap_make_str_const("firmware/hash"), 0);
        if (!resource)
        {
            ESP_LOGE(TAG, "coap_resource_init() failed for firmware/hash");
            goto clean_up;
        }
        coap_register_handler(resource, COAP_REQUEST_GET, hnd_firmware_hash_get);
        coap_add_resource(ctx, resource);

        wait_ms = COAP_RESOURCE_CHECK_TIME * 1000;

        while (1)
        {
            // Perform the operation after the delay
            time_t now = 0;
            time(&now);
            int timestamp = (int)now;

            // OTA Timeout Check
            if (otaing) {
                time_t current_time_for_ota;
                time(&current_time_for_ota);
                // Ensure last_ota_block_request_time has been initialized (i.e., at least one block request has started)
                if (last_ota_block_request_time != 0 && difftime(current_time_for_ota, last_ota_block_request_time) > 180.0) { // 3 minutes = 180 seconds
                    ESP_LOGI(TAG, "OTA timed out due to inactivity. Resetting OTA state.");
                    otaing = false;
                    firmware_transfer_completed = 0; // Reset transfer completion flag

                    if (hash_initialized_global) { // If SHA context was initialized
                        mbedtls_sha256_free(&sha256_ctx_global); // Free it
                    }
                    hash_initialized_global = false; // Reset SHA initialized flag
                    hash_finished_global = false;    // Reset SHA finished flag
                    last_ota_block_request_time = 0; // Reset OTA block request timer
                }
            }

            // // 添加丢包补偿检查代码
            // if (difftime(now, last_compensation_check_time) >= COMPENSATION_CHECK_INTERVAL) {
            //     // 执行丢包补偿检查
            //     check_and_compensate_lost_packets();
            //     last_compensation_check_time = now;
            // }

            if (timestamp - last_timestamp > 60)
            {
                if(xIsConnected) {
                    ledtask_operation(GREEN, OPEN);
                }
                // ESP_LOGW(TAG, "Sensor Disconnected!");
            }

            int result = coap_io_process(ctx, wait_ms);
            if (result < 0)
            {
                break;
            }
            else if (result && (unsigned)result < wait_ms)
            {
                /* decrement if there is a result wait time returned */
                wait_ms -= result;
            }
            if (result)
            {
                /* result must have been >= wait_ms, so reset wait_ms */
                wait_ms = COAP_RESOURCE_CHECK_TIME * 1000;
            }
        }
    }
clean_up:
    coap_free_context(ctx);
    coap_cleanup();

    vTaskDelete(NULL);
}

/**
 * @brief Initialize the CoAP server by creating a dedicated FreeRTOS task.
 */
void coap_server_init(void)
{
    xTaskCreate(coap_server, "coap_server", 4096, NULL, 5, NULL);
}