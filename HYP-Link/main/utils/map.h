#ifndef _MAP_H_
#define _MAP_H_

#include "freertos/FreeRTOS.h"
#include "freertos/queue.h"

/* Defines */
#define MAC_ADDR_STRLEN 18     // Maximum length of a MAC address string including null terminator (format: xx:xx:xx:xx:xx:xx)
#define NUM_READINGS 12        // Number of current readings per sensor

/* Global Queue for Sensor Updates */
extern QueueHandle_t sensor_update_queue;

/* Configuration */
// Set to 1 to release sensor data after generating JSON, 0 to keep data in memory
#define RELEASE_SENSOR_DATA_AFTER_JSON 1

/* Function Declarations */
void map_init_queue(void);
char* getversionByMac(char *mac_addr);
int getisPoweredByMac(char *mac_addr);
int getSleepTimeByMac(char *mac_addr);
int getTypeByMac(char *mac_addr);
int getRssiByMac(char *mac_addr);
bool getLowCurrentByMac(char *mac_addr);
void addOrUpdateFirmwareVersion(char *addr_only, uint8_t major, uint8_t minor, uint8_t patch);
void addOrUpdateCurrent(char *addr_only, float *rms);
void addOrUpdateTimestamp(char *addr_only, char *timestamp);
void addOrUpdateSleepTime(char *mac_addr, int sleep_time);
void addOrUpdateisPowered(char *mac_addr, int isPowered);
void addOrUpdateType(char *mac_addr, int type);
void addOrUpdateRssi(char *mac_addr, int rssi);
void addOrUpdateLowCurrent(char *mac_addr, bool lowCurrent);
void printCurrentVector();
void printSleepTimeVector();
void printisPoweredVector();
void displayTimestamps();
uint16_t getChildCount();
void check_and_compensate_lost_packets(void);
char *arrayToJson(char *addr_only);

// 获取message_id
uint32_t getMessageIdByMac(char *mac_addr);

// 设置message_id
void addOrUpdateMessageId(char *mac_addr, uint32_t message_id);

#endif /* _MAP_H_ */
