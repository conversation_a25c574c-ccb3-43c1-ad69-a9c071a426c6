#ifndef MQTT_MANAGER_H
#define MQTT_MANAGER_H

#include "network_transport.h"
#include "freertos/FreeRTOS.h"
#include "esp_event.h"

extern bool xIsConnected;

/**
 * @brief Register an event handler with coreMQTT-Agent events.
 *
 * @param[in] xEventHandler Event handling function.
 *
 * @return pdPASS if successful, pdFAIL otherwise.
 */
BaseType_t xCoreMqttAgentManagerRegisterHandler( esp_event_handler_t xEventHandler );

/**
 * @brief Start the coreMQTT-Agent manager.
 *
 * This handles initializing the underlying coreMQTT context, initializing
 * coreMQTT-Agent, starting the coreMQTT-Agent task, and starting the
 * connection handling task.
 *
 * @param[in] pxNetworkContextIn Pointer to the network context.
 *
 * @return pdPASS if successful, pdFAIL otherwise.
 */
BaseType_t xCoreMqttAgentManagerStart( NetworkContext_t * pxNetworkContextIn );

/**
 * @brief Posts a coreMQTT-Agent event to the default event loop.
 *
 * @param[in] lEventId Event ID of the coreMQTT-Agent event to be posted.
 *
 * @return pdPASS if successful, pdFAIL otherwise.
 */
BaseType_t xCoreMqttAgentManagerPost( int32_t lEventId );

void ppp_isConnected();
void ppp_isDisconnected();

/**
 * @brief Wait for MQTT memory to be ready for operations.
 *
 * This function blocks until the MQTT memory check is completed and
 * sufficient memory is available for MQTT operations. Should be called
 * by publishing tasks before performing MQTT operations.
 *
 * @param[in] xTicksToWait Maximum time to wait for memory to be ready.
 *                         Use portMAX_DELAY to wait indefinitely.
 *
 * @return pdTRUE if memory is ready, pdFALSE if timeout occurred.
 */
BaseType_t xCoreMqttAgentWaitForMemoryReady(TickType_t xTicksToWait);

#endif /* MQTT_MANAGER_H */
