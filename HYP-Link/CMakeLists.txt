# The following lines of boilerplate have to be in your project's CMakeLists
# in this exact order for cmake to work correctly
cmake_minimum_required(VERSION 3.10)

# (Not part of the boilerplate)
# This example uses an extra component for common functions such as Wi-Fi and Ethernet connection.
set(EXTRA_COMPONENT_DIRS "$ENV{IDF_PATH}/examples/common_components/protocol_examples_common"
    "${CMAKE_CURRENT_SOURCE_DIR}/components/esp-aws-iot/libraries/backoffAlgorithm"
    "${CMAKE_CURRENT_SOURCE_DIR}/components/esp-aws-iot/libraries/coreMQTT"
    "${CMAKE_CURRENT_SOURCE_DIR}/components/esp-aws-iot/libraries/coreMQTT-Agent"
    "${CMAKE_CURRENT_SOURCE_DIR}/components/esp-aws-iot/libraries/coreJSON"
    "${CMAKE_CURRENT_SOURCE_DIR}/components/esp-aws-iot/libraries/aws-iot-core-mqtt-file-streams-embedded-c"
    "${CMAKE_CURRENT_SOURCE_DIR}/components/esp-aws-iot/libraries/Jobs-for-AWS-IoT-embedded-sdk"
    "${CMAKE_CURRENT_SOURCE_DIR}/components/esp-aws-iot/libraries/corePKCS11"
    "${CMAKE_CURRENT_SOURCE_DIR}/components/esp-aws-iot/libraries/Fleet-Provisioning-for-AWS-IoT-embedded-sdk"
    "${CMAKE_CURRENT_SOURCE_DIR}/components/esp-aws-iot/libraries/common/posix_compat"
)

include($ENV{IDF_PATH}/tools/cmake/project.cmake)
project(hyplink)

spiffs_create_partition_image(spiffs_storage ./main/spiffs_image FLASH_IN_PROJECT)