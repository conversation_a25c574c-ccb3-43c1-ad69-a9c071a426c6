/**
 * @file test_coap_optimization.c
 * @brief Test suite for CoAP optimization improvements
 */

#include <stdio.h>
#include <string.h>
#include <time.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "esp_log.h"
#include "unity.h"

#include "map.h"

static const char *TAG = "test_coap_opt";

/**
 * @brief Test queue initialization and capacity
 */
void test_queue_initialization(void)
{
    // Initialize queues
    map_init_queue();
    
    // Verify queues are created
    TEST_ASSERT_NOT_NULL(sensor_update_queue);
    TEST_ASSERT_NOT_NULL(background_processing_queue);
    
    // Test queue capacity
    UBaseType_t sensor_queue_size = uxQueueSpacesAvailable(sensor_update_queue);
    UBaseType_t bg_queue_size = uxQueueSpacesAvailable(background_processing_queue);
    
    TEST_ASSERT_EQUAL(SENSOR_UPDATE_QUEUE_SIZE, sensor_queue_size);
    TEST_ASSERT_EQUAL(BACKGROUND_PROCESSING_QUEUE_SIZE, bg_queue_size);
    
    ESP_LOGI(TAG, "Queue initialization test passed");
}

/**
 * @brief Test non-blocking queue operations
 */
void test_non_blocking_queue_operations(void)
{
    char test_mac[] = "AA:BB:CC:DD:EE:FF";
    
    // Test non-blocking send to sensor update queue
    BaseType_t result = xQueueSend(sensor_update_queue, test_mac, 0);
    TEST_ASSERT_EQUAL(pdTRUE, result);
    
    // Verify item was queued
    UBaseType_t items_waiting = uxQueueMessagesWaiting(sensor_update_queue);
    TEST_ASSERT_EQUAL(1, items_waiting);
    
    // Test non-blocking receive
    char received_mac[MAC_ADDR_STRLEN];
    result = xQueueReceive(sensor_update_queue, received_mac, 0);
    TEST_ASSERT_EQUAL(pdTRUE, result);
    TEST_ASSERT_EQUAL_STRING(test_mac, received_mac);
    
    ESP_LOGI(TAG, "Non-blocking queue operations test passed");
}

/**
 * @brief Test background processing item structure
 */
void test_background_processing_item(void)
{
    BackgroundProcessingItem item = {0};
    
    // Initialize test item
    strcpy(item.mac_addr, "11:22:33:44:55:66");
    item.data_size = 64;
    item.sensor_type = 1;
    item.need_rssi_lookup = true;
    item.session_ptr = (void*)0x12345678;
    
    // Allocate test data
    item.raw_data = malloc(item.data_size);
    TEST_ASSERT_NOT_NULL(item.raw_data);
    
    // Fill with test pattern
    for (size_t i = 0; i < item.data_size; i++) {
        item.raw_data[i] = (uint8_t)(i & 0xFF);
    }
    
    // Test queue send/receive
    BaseType_t result = xQueueSend(background_processing_queue, &item, 0);
    TEST_ASSERT_EQUAL(pdTRUE, result);
    
    BackgroundProcessingItem received_item;
    result = xQueueReceive(background_processing_queue, &received_item, 0);
    TEST_ASSERT_EQUAL(pdTRUE, result);
    
    // Verify data integrity
    TEST_ASSERT_EQUAL_STRING(item.mac_addr, received_item.mac_addr);
    TEST_ASSERT_EQUAL(item.data_size, received_item.data_size);
    TEST_ASSERT_EQUAL(item.sensor_type, received_item.sensor_type);
    TEST_ASSERT_EQUAL(item.need_rssi_lookup, received_item.need_rssi_lookup);
    TEST_ASSERT_EQUAL(item.session_ptr, received_item.session_ptr);
    
    // Verify raw data
    for (size_t i = 0; i < item.data_size; i++) {
        TEST_ASSERT_EQUAL(item.raw_data[i], received_item.raw_data[i]);
    }
    
    // Clean up
    free(item.raw_data);
    free(received_item.raw_data);
    
    ESP_LOGI(TAG, "Background processing item test passed");
}

/**
 * @brief Test queue overflow handling
 */
void test_queue_overflow_handling(void)
{
    char test_mac[MAC_ADDR_STRLEN];
    BaseType_t result;
    int successful_sends = 0;
    
    // Fill the sensor update queue to capacity
    for (int i = 0; i < SENSOR_UPDATE_QUEUE_SIZE + 10; i++) {
        snprintf(test_mac, sizeof(test_mac), "AA:BB:CC:DD:EE:%02X", i);
        result = xQueueSend(sensor_update_queue, test_mac, 0);
        
        if (result == pdTRUE) {
            successful_sends++;
        }
    }
    
    // Should only be able to send up to queue capacity
    TEST_ASSERT_EQUAL(SENSOR_UPDATE_QUEUE_SIZE, successful_sends);
    
    // Verify queue is full
    UBaseType_t items_waiting = uxQueueMessagesWaiting(sensor_update_queue);
    TEST_ASSERT_EQUAL(SENSOR_UPDATE_QUEUE_SIZE, items_waiting);
    
    // Additional sends should fail gracefully
    result = xQueueSend(sensor_update_queue, test_mac, 0);
    TEST_ASSERT_EQUAL(pdFALSE, result);
    
    // Clean up queue
    while (uxQueueMessagesWaiting(sensor_update_queue) > 0) {
        xQueueReceive(sensor_update_queue, test_mac, 0);
    }
    
    ESP_LOGI(TAG, "Queue overflow handling test passed");
}

/**
 * @brief Performance test for queue operations
 */
void test_queue_performance(void)
{
    const int test_iterations = 1000;
    char test_mac[] = "AA:BB:CC:DD:EE:FF";
    
    // Measure send performance
    TickType_t start_time = xTaskGetTickCount();
    
    for (int i = 0; i < test_iterations; i++) {
        BaseType_t result = xQueueSend(sensor_update_queue, test_mac, 0);
        if (result == pdTRUE) {
            // Immediately receive to prevent queue overflow
            char received_mac[MAC_ADDR_STRLEN];
            xQueueReceive(sensor_update_queue, received_mac, 0);
        }
    }
    
    TickType_t end_time = xTaskGetTickCount();
    TickType_t duration = end_time - start_time;
    
    // Calculate operations per second
    float ops_per_second = (float)(test_iterations * 2) / (duration / (float)configTICK_RATE_HZ);
    
    ESP_LOGI(TAG, "Queue performance: %.2f ops/sec (%d iterations in %d ticks)", 
             ops_per_second, test_iterations * 2, duration);
    
    // Performance should be reasonable (>1000 ops/sec)
    TEST_ASSERT_GREATER_THAN(1000.0, ops_per_second);
    
    ESP_LOGI(TAG, "Queue performance test passed");
}

/**
 * @brief Test memory management in background processing
 */
void test_memory_management(void)
{
    const size_t test_data_size = 128;
    BackgroundProcessingItem item = {0};
    
    // Test multiple allocations
    for (int i = 0; i < 10; i++) {
        strcpy(item.mac_addr, "AA:BB:CC:DD:EE:FF");
        item.data_size = test_data_size;
        item.raw_data = malloc(test_data_size);
        
        TEST_ASSERT_NOT_NULL(item.raw_data);
        
        // Fill with test pattern
        memset(item.raw_data, i, test_data_size);
        
        // Send to queue
        BaseType_t result = xQueueSend(background_processing_queue, &item, 0);
        TEST_ASSERT_EQUAL(pdTRUE, result);
        
        // Receive and verify
        BackgroundProcessingItem received_item;
        result = xQueueReceive(background_processing_queue, &received_item, 0);
        TEST_ASSERT_EQUAL(pdTRUE, result);
        
        // Verify data pattern
        for (size_t j = 0; j < test_data_size; j++) {
            TEST_ASSERT_EQUAL(i, received_item.raw_data[j]);
        }
        
        // Clean up
        free(item.raw_data);
        free(received_item.raw_data);
    }
    
    ESP_LOGI(TAG, "Memory management test passed");
}

/**
 * @brief Run all optimization tests
 */
void run_coap_optimization_tests(void)
{
    ESP_LOGI(TAG, "Starting CoAP optimization tests...");
    
    RUN_TEST(test_queue_initialization);
    RUN_TEST(test_non_blocking_queue_operations);
    RUN_TEST(test_background_processing_item);
    RUN_TEST(test_queue_overflow_handling);
    RUN_TEST(test_queue_performance);
    RUN_TEST(test_memory_management);
    
    ESP_LOGI(TAG, "All CoAP optimization tests completed successfully!");
}
