# HYP-Link 项目

[English Documentation](README.md)

## 项目简介

HYP-Link 是一个基于 ESP32 的物联网设备，提供多种网络连接功能（WiFi、蓝牙、以太网、4G）及传感器数据采集上报能力。设备通过 MQTT 协议与 AWS IoT 云平台通信，支持 OTA 固件升级，具备蓝牙低功耗（BLE）配网功能。

## 系统架构

项目基于 ESP-IDF 开发框架构建，采用 FreeRTOS 实时操作系统，主要组件如下：

```
HYP-Link
│
├── main
│   ├── core         # 核心功能模块
│   ├── networking   # 网络通信模块
│   ├── tasks        # 任务模块
│   ├── utils        # 工具函数
│   ├── sensor       # 传感器相关
│   └── fpwc         # Fleet Provisioning 相关功能
│
└── components       # 第三方组件库
    └── esp-aws-iot  # AWS IoT 组件库
```

## 启动流程

1. 系统从 `main.c` 的 `app_main()` 函数启动，调用 `app_manager.c` 中的 `app_manager_init()`
2. 初始化过程会先检查设备是否已配置（通过 NVS 存储的配置标志）
   - 未配置时：进入蓝牙配网模式
   - 已配置时：进入正常工作模式

## 关键组件功能

### 1. 核心管理模块 (app_manager.c)

- LED 控制：通过 LEDC 控制设备的 RGB LED 指示灯
- 按键管理：检测长按/短按进行配置重置等操作
- 证书管理：初始化和管理 TLS 证书
- 网络初始化：启动 WiFi、以太网、4G 等网络接口
- 任务管理：创建和启动各个功能线程

### 2. 蓝牙配网模块 (ble_spp_server.c)

- 实现基于 NimBLE 的 BLE SPP (Serial Port Profile) 服务
- 广播名称为 "HYP-Link-Config" 的蓝牙信号
- 处理手机/电脑发送的配置命令，例如：
  - `WIFI:ssid,password` - 配置 WiFi 凭据
  - `ETH:1` 或 `ETH:0` - 启用/禁用以太网
  - `4G:1` 或 `4G:0` - 启用/禁用 4G 连接
  - `SAVE` - 保存配置并重启设备

### 3. WiFi 模块 (app_wifi.c)

- 支持多种 WiFi 配置方式：蓝牙配网、软 AP 配网
- 管理 WiFi 连接状态和自动重连
- 存储 WiFi 凭据到 NVS

### 4. MQTT 管理模块 (mqtt_manager.c)

- 基于 AWS coreMQTT-Agent 的 MQTT 客户端实现
- 管理 MQTT 连接、订阅、发布等操作
- 提供消息回调机制
- 处理网络断开自动重连逻辑

### 5. 数据上报模块 (reporting_task.c)

- 定期采集传感器数据发布到 MQTT 服务器
- 实现订阅回调处理下行指令
- 支持 LED 远程控制功能

### 6. OTA 模块 (ota_task.c)

- 基于 AWS IoT Jobs 的 OTA 更新实现
- 支持固件验证和安全启动
- 支持流式下载大型固件文件
- 实现固件更新后自动回滚功能

### 7. 网络组件

- SNTP 客户端：同步网络时间
- PPPoS 客户端：4G 蜂窝网络连接
- CoAP 服务器：支持 CoAP 协议通信

## 存储配置

- 使用 NVS (非易失性存储) 保存设备配置
- 分区表包含多个功能区：
  - `nvs`: 一般配置存储
  - `storage`: 加密配置存储
  - `ota_0`/`ota_1`: OTA 分区
  - `rcp_fw`: OpenThread 固件存储
  - `spiffs_storage`: SPIFFS 文件系统
  - `esp_secure_cert`: 安全证书存储

## 设备状态指示

设备使用 RGB LED 指示不同工作状态：

- 红灯快闪：设备初始化中
- 蓝灯快闪：蓝牙配网模式
- 蓝灯慢闪：蓝牙已连接，等待配置
- 绿灯快闪：设备正在启动
- 绿灯慢闪：设备正常工作

## 恢复出厂设置

长按按键（GPIO 46）10秒可以擦除 NVS 分区，重置设备至未配置状态。

---

# 目录
- [目录](#目录)
- [如何在 AWS 上配置网关](#如何在-aws-上配置网关)
    - [①打开 AWS IOT ---\> Security ---\> Certificate Authority ---\> Register CA Certificate](#打开-aws-iot-----security-----certificate-authority-----register-ca-certificate)
    - [②Security ---\> Policies ---\> Create Policy](#security-----policies-----create-policy)
    - [③Connect ---\> Connect multiple devices ---\> Create a preset template](#connect-----connect-multiple-devices-----create-a-preset-template)
    - [④Test ---\> MQTT Test Client ---\> Connection Details ---\> Copy Endpoint](#test-----mqtt-test-client-----connection-details-----copy-endpoint)
- [如何烧录证书](#如何烧录证书)
- [如何烧录 HYP-LINK](#如何烧录-hyp-link)
  - [1.第一种方法](#1第一种方法)
    - [① 搭建环境（esp-idf 版本 v5.3.1）](#-搭建环境esp-idf-版本-v531)
    - [② 修改源代码](#-修改源代码)
    - [③ 按照①所示按下烧录按钮](#-按照所示按下烧录按钮)
  - [2.第二种方法](#2第二种方法)
    - [① 运行以下代码](#-运行以下代码)
- [如何进行 OTA 升级](#如何进行-ota-升级)


# 如何在 AWS 上配置网关

### ①打开 AWS IOT ---> Security ---> Certificate Authority ---> Register CA Certificate

使用 [OpenSSL v1.1.1i](https://www.openssl.org/) 工具创建 CA 证书

```
openssl genrsa -out root_CA_key_filename.key 2048
```

```
openssl req -x509 -new -nodes \
    -key root_CA_key_filename.key \
    -sha256 -days 1024 \
    -out root_CA_cert_filename.pem
```

![image](https://github.com/user-attachments/assets/87cbfcf2-d293-4595-af77-55a1f0e60bb0)

### ②Security ---> Policies ---> Create Policy

Hyp-Link

```
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": "iot:Connect",
      "Resource": "arn:aws:iot:aws-region:aws-account-id:*"
    },
    {
      "Effect": "Allow",
      "Action": "iot:Publish",
      "Resource": "arn:aws:iot:aws-region:aws-account-id:*"
    },
    {
      "Effect": "Allow",
      "Action": "iot:Subscribe",
      "Resource": "arn:aws:iot:aws-region:aws-account-id:*"
    },
    {
      "Effect": "Allow",
      "Action": "iot:Receive",
      "Resource": "arn:aws:iot:aws-region:aws-account-id:*"
    }
  ]
}
```

- `aws-region`: 替换为区域，例如：eu-central-1
- `aws-account-id`: 替换为账户 ID

### ③Connect ---> Connect multiple devices ---> Create a preset template

JITP

![image](https://github.com/user-attachments/assets/97948fbc-32f5-4fa1-9844-17b4dffc9a4e)


![image](https://github.com/user-attachments/assets/f73fe7f4-f370-4f41-9d51-42b4a35343fe)


![image](https://github.com/user-attachments/assets/********-cad8-4477-9bd9-b841835f36db)


![image](https://github.com/user-attachments/assets/8f2cc024-4ddb-4af3-8a96-************)

### ④Test ---> MQTT Test Client ---> Connection Details ---> Copy Endpoint

将 `sdkconfig.hyplink.example` 重命名为 `sdkconfig.hyplink` 并替换以下内容：

- `MQTT_BROKER_ENDPOINT`
- `GRI_MQTT_ENDPOINT`

将其替换为创建预设队列时创建的名称：

- `PROVISIONING_TEMPLATE_NAME`


# 如何烧录证书

在根目录中运行以下命令

```python
python managed_components/espressif__esp_secure_cert_mgr/tools/configure_esp_secure_cert.py -p PORT --keep_ds_data_on_host --ca-cert main/spiffs_image/certs/AmazonRootCA1.pem --device-cert main/spiffs_image/certs/device_cert.pem.crt --private-key main/spiffs_image/certs/device_private.pem.key --target_chip esp32s3 --secure_cert_type cust_flash --priv_key_algo RSA 2048
```

- `PORT`: ESP32-S3 开发板连接的串行端口。您可以参考此[指南](https://docs.espressif.com/projects/esp-idf/en/stable/esp32/get-started/establish-serial-connection.html)了解如何找到该值。


# 如何烧录 HYP-LINK


## 1.第一种方法

### ① 搭建环境（esp-idf 版本 v5.3.1）

[espressif/esp-idf: 乐鑫物联网开发框架。乐鑫 SoC 的官方开发框架。(github.com)](https://github.com/espressif/esp-idf?tab=readme-ov-file)

### ② 修改源代码

目录：
```
esp\v5.3.1\esp-idf\examples\common_components\protocol_examples_common\eth_connect.c
237        xSemaphoreTake(s_semph_get_ip_addrs, 200);
240        xSemaphoreTake(s_semph_get_ip6_addrs, 200);
```

### ③ 按照①所示按下烧录按钮

​                 

## 2.第二种方法

### ① 运行以下代码

```
python.exe esp\v5.3.1\esp-idf\components\esptool_py\esptool\esptool.py -p PORT -b 460800 --before default_reset --after hard_reset --chip esp32s3 --no-stub write_flash --flash_mode dio --flash_freq 80m --flash_size 8MB 0x0 bootloader/bootloader.bin 0x30000 hyplink.bin 0xb000 partition_table/partition-table.bin 0x25000 ota_data_initial.bin 0x521000 rcp_fw.bin 0x5c1000 spiffs_storage.bin
```

- `PORT`: ESP32-S3 开发板连接的串行端口。

# 如何进行 OTA 升级

上传具有更高版本号的二进制文件（在步骤 5.3 中创建）并创建 OTA 更新任务

1. 在 AWS IoT 控制台的导航窗格中，选择"管理"，然后选择"任务"。选择"创建任务"。
2. 在"创建 FreeRTOS 空中升级 (OTA) 更新任务"旁边，选择"创建 FreeRTOS OTA 更新任务"。提供任务名称并点击"下一步"。
3. 您可以将 OTA 更新部署到单个设备或一组设备。在"要更新的设备"下，选择您之前创建的事物。您可以在 AWS IoT->管理->事物下找到它。如果您正在更新一组设备，请选中与您的设备关联的事物组旁边的复选框。
4. 在"选择文件传输协议"下，选择"MQTT"。
5. 在"签名并选择您的文件"下，选择"为我签名新文件"。
6. 在"代码签名配置文件"下，选择"创建新配置文件"。
7. 在"创建代码签名配置文件"中：
   1. 输入此配置文件的名称。
   2. 对于设备硬件平台，选择：'ESP32-DevKitC'。
   3. 在代码签名证书下，选择"选择现有证书"，然后选择您之前使用 AWS CLI 创建并在 AWS ACM 中注册的证书（您可以使用 ARN 识别它）。
   4. 在"设备上代码签名证书的路径名"下，输入"/"。（这不适用于 ESP32-C3，因此 / 仅是填充符。）
   5. 点击"创建"。确认代码签名配置文件已成功创建。
8. 返回 FreeRTOS OTA 任务控制台：
   1. 在"代码签名配置文件"下，从下拉列表中选择刚刚创建的代码签名配置文件。
   2. 在"文件"下，选择"上传新文件"，然后点击"选择文件"。会弹出文件浏览器。选择具有更高版本号的已签名二进制映像。
   3. 在"S3 中的文件上传位置"下，点击"浏览 S3"，然后选择您为此任务创建的 S3 存储桶。点击"选择"。
   4. 在"设备上文件的路径名"下，输入"NA"。
   5. 在"OTA 更新任务的 IAM 角色"下，从下拉列表中选择您之前为 OTA 更新创建的角色。
   6. 点击"下一步"，然后点击"创建任务"。确认任务已成功创建。注意：如果无法创建 OTA 任务，请确保此 OTA 任务更新的角色具有正确的权限（策略）。 