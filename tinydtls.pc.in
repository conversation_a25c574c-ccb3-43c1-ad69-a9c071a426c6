# pkg-config file for tinydtls
#
#
# Copyright (c) 2011-2021 <PERSON> (TZI) and others.
# All rights reserved. This program and the accompanying materials
# are made available under the terms of the Eclipse Public License v1.0
# and Eclipse Distribution License v. 1.0 which accompanies this distribution.
#
# The Eclipse Public License is available at http://www.eclipse.org/legal/epl-v10.html
# and the Eclipse Distribution License is available at
# http://www.eclipse.org/org/documents/edl-v10.php.
#
# Contributors:
#    <PERSON>     - support for pkg-config in tinydtls

prefix=@prefix@
exec_prefix=@exec_prefix@
includedir=@includedir@
libdir=@libdir@

Name: @PACKAGE_NAME@
Description: A C library for Datagram Transport Layer Security (DTLS) supporting both client and server functionality.
URL: @PACKAGE_URL@
Version: @PACKAGE_VERSION@
Libs: -L${libdir} -ltinydtls
Cflags: -I${includedir}/tinydtls
