/* coap_hashkey.c -- definition of hash key type and helper functions
 *
 * Copyright (C) 2010-2024 <PERSON> <berg<PERSON>@tzi.org>
 *
 * SPDX-License-Identifier: BSD-2-Clause
 *
 * This file is part of the CoAP library libcoap. Please see
 * README for terms of use.
 */

/**
 * @file coap_hashkey.c
 * @brief Hashkey functions
 */

#include "coap3/coap_libcoap_build.h"

void
coap_hash_impl(const unsigned char *s, size_t len, coap_key_t h) {
  size_t j;

  while (len--) {
    j = sizeof(coap_key_t)-1;

    while (j) {
      h[j] = ((h[j] << 7) | (h[j-1] >> 1)) + h[j];
      --j;
    }

    h[0] = (h[0] << 7) + h[0] + *s++;
  }
}
