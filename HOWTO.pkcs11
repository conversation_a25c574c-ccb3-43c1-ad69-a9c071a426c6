#
# Using PKCS11 with libcoap.
#
# This HOWTO works for CentOS 7.
#
# As CentOS 7 uses OpenSSL prior to 1.1.0, dual OpenSSL support needs to be
# set up and used for libcoap.  See HOWTO.dual.openssl for setting this up.
#
# It also is possible that you want to use GnuTLS - and want to use a later
# version.  HOWTO.dual.gnutls for setting this up.
#
# OpenSSL and GnuTLS are currently supported
#

############################################################################
#
# Testing examples
#
############################################################################
#
# Update PKCS11 token with certificates and keys
#
# Assumption is that you already have the following PEM files
#  ca-cert.pem     - The certificate of the CA that signed Server and Client
#  server-cert.pem - Contains the server certificate in PEM format
#  server-key.pem  - Contains the server private key in PEM format
#  client-cert.pem - Contains the server certificate in PEM format
#  client-key.pem  - Contains the server private key in PEM format
#
# Tokens will be stored under /var/lib/softhsm/tokens/
#
# The user you are running this as needs to be in the group defined for
# /var/lib/softhsm/tokens/.  E.g.
#  $ sudo ls -ld  /var/lib/softhsm/tokens/
#  drwxrws--- 3 root softhsm 4096 May  3 09:52 /var/lib/softhsm/tokens/
# which is softhsm in this case (It could be ods). To verify if you are in
# the correct group
#  $ id
# To add user to this group
#  $ sudo usermod -a -G softhsm <user>
# and log out and back in again.
#

# Set libsofthsm2.so to use (may be /usr/lib/softhsm/libsofthsm2.so)
LIBSOFTHSM=/usr/local/lib/softhsm/libsofthsm2.so

# Initialize Soft HSM token
#  Note: slot 0 is re-allocated to slot XXX.  This is presented as a decimal
# number, the hex equivalent (leading 0x) can be used for any slot options..
# Set SO PIN to 4321, user PIN to 1234
softhsm2-util --init-token --slot 0 --label "token-0" --pin 1234 --so-pin 4321

# CA Certificate (different id to Server/Client Public Certificate)
# (GnuTLS requires this to be trusted)
p11tool --so-login --load-certificate ca-cert.pem --write --label ca-cert \
  --set-so-pin 4321 --id cc00 --mark-trusted "pkcs11:token=token-0"

# Server Private Key
openssl pkcs8 -topk8 -inform PEM -outform PEM -in server-key.pem \
  -out server-key.pk8 -nocrypt
softhsm2-util --import server-key.pk8 --label "server-key" --id aa00 \
  --pin 1234 --token "token-0"

# Server Public Certificate
# (Use different id to private key, but not the same as CA/Client cert)
openssl x509 -in server-cert.pem -out server-cert.der -outform DER
pkcs11-tool --module $LIBSOFTHSM --pin 1234 \
  --write-object ./server-cert.der --type cert --id aa01 \
  --label "server-cert" --token-label "token-0"

# Client Private Key
openssl pkcs8 -topk8 -inform PEM -outform PEM -in client-key.pem \
  -out client-key.pk8 -nocrypt
softhsm2-util --import client-key.pk8 --label "client-key" --id bb00 \
  --pin 1234 --token "token-0"

# Client Public Certificate
# (Use different id to private key, but not the same as CA/Client cert)
openssl x509 -in client-cert.pem -out client-cert.der -outform DER
pkcs11-tool --module $LIBSOFTHSM --pin 1234 \
  --write-object ./client-cert.der --type cert --id bb01 \
  --label "client-cert" --token-label "token-0"

# Verify token is correctly populated
pkcs11-tool --module=$LIBSOFTHSM -t
pkcs11-tool --module=$LIBSOFTHSM --list-objects \
  --pin 1234 --token-label "token-0"
p11tool --list-all pkcs11:model=SoftHSM%20v2

#
# Run coap-server using PKCS11 (-C option may need to be -C cert.der)
#
coap-server -C 'pkcs11:token=token-0;id=%cc%00?pin-value=1234' \
  -c 'pkcs11:token=token-0;id=%aa%01?pin-value=1234' \
  -j 'pkcs11:token=token-0;id=%aa%00?pin-value=1234' -v9

# or
coap-server -C 'pkcs11:token=token-0;id=%cc%00' \
  -c 'pkcs11:token=token-0;id=%aa%01' \
  -j 'pkcs11:token=token-0;id=%aa%00' -J 1234 -v9

# or
coap-server -C 'pkcs11:token=token-0;object=ca-cert' \
  -c 'pkcs11:token=token-0;object=server-cert' \
  -j 'pkcs11:token=token-0;object=server-key' -J 1234 -v9

#
# Run coap-client using PKCS11 (-C option may need to be -C cert.der)
#
coap-client -C 'pkcs11:token=token-0;id=%cc%00?pin-value=1234' \
  -c 'pkcs11:token=token-0;id=%bb%01?pin-value=1234' \
  -j 'pkcs11:token=token-0;id=%bb%00?pin-value=1234' -v9 coaps://[::1]

# or
coap-client -C 'pkcs11:token=token-0;id=%cc%00' \
  -c 'pkcs11:token=token-0;id=%bb%01' \
  -j 'pkcs11:token=token-0;id=%bb%00' -J 1234 -v9 coaps://[::1]

# or
coap-client -C 'pkcs11:token=token-0;object=ca-cert' \
  -c 'pkcs11:token=token-0;object=client-cert' \
  -j 'pkcs11:token=token-0;object=client-key' -J 1234 -v9 coaps://[::1]

#
# Client and Server using RPK (GnuTLS only)
#
coap-server -M 'pkcs11:token=token-0;object=server-key' -J 1234 -v9
# and
coap-client -M 'pkcs11:token=token-0;object=client-key' -J 1234 -v9 coaps://[::1]
