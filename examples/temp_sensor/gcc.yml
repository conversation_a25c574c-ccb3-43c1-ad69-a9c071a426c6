compiler:
  path: gcc
  source_path:     'src/'
  unit_tests_path: &unit_tests_path 'test/'
  build_path:      &build_path 'build/'
  options:
    - -c
  includes:
    prefix: '-I'
    items:
      - 'src/'
      - '../../src/'
      - '../../vendor/unity/src/'
      - '../../vendor/unity/examples/example_3/helper/'
      - './build/mocks/'
      - *unit_tests_path
  defines:
    prefix: '-D'
    items:
      - __monitor
  object_files:
    prefix: '-o'
    extension: '.o'
    destination: *build_path
linker:
  path: gcc
  options:
    - -lm
  includes:
    prefix: '-I'
  object_files:
    path: *build_path
    extension: '.o'
  bin_files:
    prefix: '-o'
    extension: '.exe'
    destination: *build_path
:cmock:
  :plugins: []
  :includes:
    - Types.h
  :mock_path: ./build/mocks

colour: true
