Example of libcoap running on Contiki-NG
========================================

To run the example, do

    $ make
    $ sudo ./server.native

and query `coap://[fd00::302:304:506:708]/time?ticks` with any coap tool,
or query `coap://[fd00::302:304:506:708]/.well-known/core`

This will

* download Contiki-NG from the upstream git sources
* build the server application
* run the server application, creating a virtual network device tap0 (unless
  that exists)
* configure your network interface to make the server accessible.

* return the appropriate response from the server to the client.

The server creates a resource for 'time' with a query 'ticks'.  This is
reported for `.well-known/core`. The work flow for adding more resources does
not differ from regular libcoap usage.
