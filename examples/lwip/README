Example of libcoap running on lwIP
==================================

To run the server example, do

    $ make
    $ sudo ./server                 # No TinyDTLS support
       or
    $ sudo ./server-dtls            # With TinyDTLS support

(and in a second terminal)

    $ sudo ip a a dev tap0 *************/24

and query `coap://*************/time?ticks` with any coap tool,
or query `coap://*************/.well-known/core`.
(If server-dtls is running, you can use coaps:// as appropriate.)

This will

* download lwip and lwip-contrib from the upstream git sources
* build the server application
* run the server application, creating a virtual network device tap0 (unless
  that exists)
* configure your network interface to make the server accessible.

* return the appropriate response from the server to the client.

The server supports the following options
"-k PSK" option where <PERSON><PERSON> defines the DTLS PSK to use (default is "secretPSK").
         (Only works for server-dtls.)
"-v level" option where logging "level" can be 0 to 7 (default is 4).
"-V level" option where DTLS logging "level" can be 0 to 7 (default is 3).
           (Only works for server-dtls.)

The server creates a resource for 'time' with a query 'ticks'.  This is
reported for `.well-known/core`. The work flow for adding more resources does
not differ from regular libcoap usage. If you seem to run out of memory
creating the resources, tweak the number of pre-allocated resources
in `config/lwippools.h`.

To run the client example

    $ make
    $ sudo ./client                 # No TinyDTLS support
       or
    $ sudo ./client-dtls            # With TinyDTLS support

As client (or client-dtls) tries to connect to coap://libcoap.net/, the tap0
interface will need IP forwarding enabled

    $ sudo sysctl -w net.ipv4.conf.default.forwarding=1

Then you will need IP forwarding enabled on your public interface
(where ens32 is replaced by your public facing interface name)
for response packets

    $ sudo sysctl -w net.ipv4.conf.ens32.forwarding=1

As well as the interface connecting to the internet will need a NAT rule to
masquerade the internal IP address (*************) to the IP of the outgoing
interface (where ens32 is replaced by your public facing interface name)

    $ sudo iptables -t nat -A POSTROUTING -o ens32 -j MASQUERADE

The client supports the following options
"-k PSK" option where PSK defines the DTLS PSK to use (default is "secretPSK").
         (Only works for client-dtls.)
"-u id" option where id defines the DTLS id to use (default is "abc").
         (Only works for client-dtls.)
"-v level" option where logging "level" can be 0 to 7 (default is 4).
"-V level" option where DTLS logging "level" can be 0 to 7 (default is 3).
           (Only works for client-dtls.)

The client supports an optional parameter which is the CoAP URI to connect to
.e.g "coap://libcoap.net/.well-known/core".  The default
is "coap://libcoap.net/" for client and client-dtls.
Using "coaps://libcoap.net" will establish a DTLS session if there is
DTLS support compiled in (client-dtls).
