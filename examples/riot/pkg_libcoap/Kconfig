# Copyright (c) 2019 HAW Hamburg
#
# This file is subject to the terms and conditions of the GNU Lesser
# General Public License v2.1. See the file LICENSE in the top level
# directory for more details.
#
menu "libcoap"
    depends on USEPKG_LIBCOAP

choice LIBCOAP_DEBUG_LEVEL
    bool "Set CoAP debugging level"
    default LIBCOAP_LOG_INFO
    help
        Set CoAP debugging level

    config LIBCOAP_LOG_EMERG
        bool "Emergency"
    config LIBCOAP_LOG_ALERT
        bool "Alert"
    config LIBCOAP_LOG_CRIT
        bool "Critical"
    config LIBCOAP_LOG_ERROR
        bool "Error"
    config LIBCOAP_LOG_WARNING
        bool "Warning"
    config LIBCOAP_LOG_NOTICE
        bool "Notice"
    config LIBCOAP_LOG_INFO
        bool "Info"
    config LIBCOAP_LOG_DEBUG
        bool "Debug"
    config LIBCOAP_LOG_OSCORE
        bool "OSCORE"
endchoice

config LIBCOAP_MAX_LOGGING_LEVEL
    int
    default 0 if LIBCOAP_LOG_EMERG
    default 1 if LIBCOAP_LOG_ALERT
    default 2 if LIBCOAP_LOG_CRIT
    default 3 if LIBCOAP_LOG_ERROR
    default 4 if LIBCOAP_LOG_WARNING
    default 5 if LIBCOAP_LOG_NOTICE
    default 6 if LIBCOAP_LOG_INFO
    default 7 if LIBCOAP_LOG_DEBUG
    default 8 if LIBCOAP_LOG_OSCORE

config LIBCOAP_IPV4_SUPPORT
    bool "Enable IPv4 support within CoAP"
    default n
    help
        Enable IPv4 functionality for CoAP.

        If this option is disabled, redundant CoAP IPv4 code is removed.
        [RIOT sock gnrc does not support this]

config LIBCOAP_IPV6_SUPPORT
    bool "Enable IPv6 support within CoAP"
    default y
    help
        Enable IPv6 functionality for CoAP.

        If this option is disabled, redundant CoAP IPv6 code is removed.

if USEMODULE_SOCK_TCP

config LIBCOAP_TCP_SUPPORT
    bool "Enable TCP support within CoAP"
    default n
    help
        Enable TCP functionality for CoAP. This is required if TLS sessions
        are to be used. Note that RIOT TCP support also needs to be enabled.

        If this option is disabled, redundant CoAP TCP code is removed.
        [RIOT sock gnrc does not support this]

endif # USEMODULE_SOCK_TCP

if USEMODULE_LIBCOAP_OSCORE

config LIBCOAP_OSCORE_SUPPORT
    bool "Enable OSCORE support within CoAP"
    default y
    help
        Enable OSCORE functionality for CoAP.

        If this option is disabled, redundant CoAP OSCORE code is removed.

endif # MODULE_LIBCOAP_OSCORE

config LIBCOAP_OBSERVE_PERSIST
    bool "Enable Observe persist support within CoAP"
    default n
    help
        Enable Observe persist functionality for CoAP.

        If this option is disabled, redundant CoAP Observe persist code is removed.

config LIBCOAP_WS_SOCKET
    bool "Enable WebSocket support within CoAP"
    default n
    help
        Enable WebSocket functionality for CoAP.

        If this option is disabled, redundant CoAP WebSocket code is removed.

config LIBCOAP_Q_BLOCK_SUPPORT
    bool "Enable Q-Block (RFC9177) support within CoAP"
    default n
    help
        Enable Q-Block (RFC9177) functionality for CoAP.

        If this option is disabled, redundant CoAP Q-Block code is removed.

config LIBCOAP_ASYNC_SUPPORT
    bool "Enable separate responses support within CoAP"
    default y
    help
        Enable async separate responses functionality for CoAP.

        If this option is disabled, redundent CoAP async separate responses code is removed.

config LIBCOAP_THREAD_SAFE
    bool "Enable thread safe support within CoAP"
    default n
    help
        Enable thread safe support within CoAP.

        If this option is disabled, libcoap is not thread safe,

config LIBCOAP_THREAD_RECURSIVE_CHECK
    bool "Enable thread recursive lock detection if thread safe support is enabled"
    depends on LIBCOAP_THREAD_SAFE
    default n
    help
        Enable thread recursive lock detection if thread safe support is enabled.

        If this option is disabled, there is no multi thread recursive detection.

config LIBCOAP_CLIENT_SUPPORT
    bool "Enable Client functionality within CoAP"
    default n
    help
        Enable client functionality (ability to make requests and receive
        responses) for CoAP. If the server is going to act as a proxy, then
        this needs to be enabled to support the ongoing session going to
        the next hop.

        If this option is disabled, redundant CoAP client only code is
        removed.
        If both this option and LIBCOAP_SERVER_SUPPORT are disabled, then
        both are automatically enabled for backwards compatability.

if LIBCOAP_CLIENT_SUPPORT

config LIBCOAP_MAX_LG_CRCVS
    int "Max number of client large receives supported"
    default 1
    help
        The maximum number of supported client large receives.

endif # LIBCOAP_CLIENT_SUPPORT

config LIBCOAP_SERVER_SUPPORT
    bool "Enable Server functionality within CoAP"
    default n
    help
        Enable server functionality (ability to receive requests and send
        responses) for CoAP.

        If this option is disabled, redundant CoAP server only code is
        removed.
        If both this option and LIBCOAP_CLIENT_SUPPORT are disabled, then
        both are automatically enabled for backwards compatability.

if LIBCOAP_SERVER_SUPPORT

config LIBCOAP_MAX_ENDPOINTS
    int "Max number of endpoints supported"
    default 4 if LIBCOAP_TCP_SUPPORT
    default 2
    help
        The maximum number of supported endpoints.

config LIBCOAP_MAX_RESOURCES
    int "Max number of resources supported"
    default 8
    help
        The maximum number of supported resources.

config LIBCOAP_MAX_ATTRIBUTE_SIZE
    int "Max size of attribute memory allocation"
    default 16
    help
        The maximum size of a supported attribute.

config LIBCOAP_MAX_ATTRIBUTES
    int "Max number of resource attributes supported"
    default 32
    help
        The maximum number of supported resource attributes.

config LIBCOAP_MAX_CACHE_KEYS
    int "Max number of cache keys supported"
    default 2
    help
        The maximum number of supported cache keys.

config LIBCOAP_MAX_CACHE_ENTRIES
    int "Max number of cache entries supported"
    default 2
    help
        The maximum number of supported cache entries.

config LIBCOAP_MAX_LG_SRCVS
    int "Max number of server large receives supported"
    default 2
    help
        The maximum number of supported server large receives.

endif # LIBCOAP_SERVER_SUPPORT

config LIBCOAP_PROXY_SUPPORT
    bool "Enable Proxy functionality within CoAP"
    depends on LIBCOAP_CLIENT_SUPPORT && LIBCOAP_SERVER_SUPPORT
    default n
    help
        Enable Proxy functionality (ability to receive requests, pass
        them to an upstream server and send back responses to client)
        for CoAP.

        If this option is disabled, redundant CoAP proxy only code is
        removed.

config LIBCOAP_MAX_LG_XMITS
    int "Max number of large transmits supported"
    default 2 if LIBCOAP_SERVER_SUPPORT
    default 1
    help
        The maximum number of supported large transmits.

config LIBCOAP_MAX_STRING_SIZE
    int "Max size of string memory allocation"
    default 64
    help
        The maximum size of a supported string.

config LIBCOAP_MAX_STRINGS
    int "Max number of strings supported"
    default 16
    help
        The maximum number of supported strings.

config LIBCOAP_MAX_PACKETS
    int "Max number of packets supported"
    default 4
    help
        The maximum number of supported packets.

config LIBCOAP_MAX_NODES
    int "Max number of nodes supported"
    default 4
    help
        The maximum number of supported nodes.

config LIBCOAP_MAX_CONTEXTS
    int "Max number of contexts supported"
    default 1
    help
        The maximum number of supported contexts.

config LIBCOAP_MAX_PDUS
    int "Max number of PDUs supported"
    default 4
    help
        The maximum number of supported PDUs.

config LIBCOAP_MAX_DTLS_SESSIONS
    int "Max number of DTLS sessions supported"
    default 2
    help
        The maximum number of supported DTLS sessions.

config LIBCOAP_MAX_SESSIONS
    int "Max number of sessions supported"
    default 4
    help
        The maximum number of supported sessions.

config LIBCOAP_MAX_OPTION_SIZE
    int "Max size of option memory allocation"
    default 16
    help
        The maximum size of a supported option.

config LIBCOAP_MAX_OPTIONS
    int "Max number of options supported"
    default 16
    help
        The maximum number of supported options.

endmenu # libcoap
