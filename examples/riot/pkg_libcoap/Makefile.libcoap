MODULE := libcoap

SRC := coap_address.c \
  coap_asn1.c \
  coap_async.c \
  coap_block.c \
  coap_cache.c \
  coap_debug.c \
  coap_dtls.c \
  coap_encode.c \
  coap_event.c \
  coap_gnutls.c\
  coap_hashkey.c \
  coap_io.c \
  coap_io_riot.c\
  coap_layers.c\
  coap_mbedtls.c\
  coap_mem.c \
  coap_net.c \
  coap_netif.c \
  coap_notls.c \
  coap_openssl.c\
  coap_option.c \
  coap_oscore.c \
  coap_pdu.c \
  coap_prng.c \
  coap_proxy.c \
  coap_resource.c \
  coap_session.c \
  coap_sha1.c \
  coap_str.c \
  coap_subscribe.c \
  coap_tcp.c \
  coap_threadsafe.c \
  coap_tinydtls.c \
  coap_uri.c \
  coap_ws.c

ifneq (,$(filter libcoap_tinydtls,$(USEMODULE)))
CFLAGS += -DCOAP_WITH_LIBTINYDTLS
endif

include $(RIOTBASE)/Makefile.base
