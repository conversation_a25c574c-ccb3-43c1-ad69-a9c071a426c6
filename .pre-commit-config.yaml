repos:
-   repo: https://github.com/igrr/astyle_py.git
    rev: v1.0.5
    hooks:
    -   id: astyle_py
        files: '^.*\.(c|cpp|cxx|h|h.in|h.riot|h.riot.in|h.windows|h.windows.in|h.contiki|hpp|inc)$'
        exclude: '^.*/(coap_uthash_internal.h|coap_utlist_internal.h)$|examples/riot/examples_libcoap_.*$|examples/riot/tests_pkg_libcoap/.*$|zephyr/.*$'
        args: ['--style=google',
               '--align-pointer=name',
               '--align-reference=name',
               '--attach-closing-while',
               '--attach-return-type-decl',
               '--break-after-logical',
               '--break-one-line-headers',
               '--break-return-type',
               '--convert-tabs',
               '--indent-continuation=2',
               '--indent-preproc-define',
               '--indent=spaces=2',
               '--max-code-length=100',
               '--max-continuation-indent=60',
               '--min-conditional-indent=0',
               '--pad-header',
               '--unpad-paren']

-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.3.0
    hooks:
    -   id: trailing-whitespace
    -   id: end-of-file-fixer
    -   id: check-merge-conflict
