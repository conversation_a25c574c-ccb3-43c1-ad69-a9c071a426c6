This is a simple file for all kinds of stuff related on development for
libcoap. Please append (and remove) any issue you think its worthy.

Classification of issues:
 Critical -> Break the library in some kind or a missing feature, maybe not
             directly but later
 Serious  -> No regression on the user side, more likly on the libcoap
             development
 Minor    -> Things that are nice to have, but they are not time critical

=================
* CRITICAL ISSUES
=================

================
* SERIOUS ISSUES
================
-> Create some development rules like:
    --> How to submit patches? What about pull requests?
	--> How to implement/change platform related code?
-> Further improve the API documentation

==============
* MINOR ISSUES
==============
-> Adding a logo for libcoap
