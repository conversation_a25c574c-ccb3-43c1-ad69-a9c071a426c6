       #######################################################
       #  Developer information for contributing to libcoap  #
       #######################################################

1. The basics
~~~~~~~~~~~~~
The libcoap project is a FOSS project that is dual licensed. The maintainer
for the libcoap is <PERSON> <<EMAIL>>.
Any contributions have to be made under the terms of the
license

  * BSD 2-Clause (The BSD 2-Clause License)

Contributions made up to 2017-06-01 have been made under the dual
license model BSD 2-Clause and GPL v2+ (The GNU General Public License
2.0 or later).

The used VCS for libcoap is Git, the main repository is living on GitHub.
You can clone (or fork directly on GitHub) on the repository site:

  https://github.com/obgm/libcoap

Please refer also to the libcoap website for additional information

  https://libcoap.net/

The build environment is grounded on the classical autotools or CMake, the
GNU GCC and the LLVM C-compiler (CLang) are supported. The Windows VS build
systems are supported.

Doxygen is used for creating a HTML based online documentation of the
libcoap library.

2. Communications
~~~~~~~~~~~~~~~~~
The best method to raise an Issue is to raise it at:

   https://github.com/obgm/libcoap/issues

Previous issues and fixes can easily be researched here.

The best method to create a patch is to create a Pull Request at:

  https://github.com/obgm/libcoap/pulls

Otherwise, the main discussion and development platform for libcoap is the
mailing list on Sourceforge.

No matter if you just have a simple question, some specific problem or
want to discuss some patches, please write it to the mailing list. Please
avoid personal mailings to the maintainer (or some other contributor) if
your questions will probably be in the interest of other users too.
You can subscribe to the list here:

  https://lists.sourceforge.net/lists/listinfo/libcoap-developers

The archive of the list can be found on:

  https://sourceforge.net/p/libcoap/mailman/libcoap-developers

Alternatively, Issues can be raised at https://github.com/obgm/libcoap/issues

3. Starting contributing
~~~~~~~~~~~~~~~~~~~~~~~~
As written above, libcoap is maintained with the Git tools so you should be
familiar with the various git commands.

The libcoap project is using just two main branches, the 'main' branch is
holding the point releases, all the development process is going on in the
'develop' branch.

To start any contributing you first have to clone the git tree from the main
repository on GitHub:

  git clone https://github.com/obgm/libcoap.git

4. Working on the source
~~~~~~~~~~~~~~~~~~~~~~~~
As one golden rule you should work on improvements within *your* own local
development branch! To do so you have to first checkout the 'develop' branch
as local branch and then start on top on this branch your own branch. So
create (or better say checkout) the local 'develop' branch:

  cd libcoap
  git checkout develop origin/develop

Now you can simply start your own local branch (for example 'my-develop')
with the 'origin/develop' as parent so you can later create the patches
against the the upstream development branch:

  git checkout -b my-develop

At this point you can now work with git, modify the source, commit
the changes, amend if needed and test your work.

At some point you will have to generate patches to post them on the mailing
list (and/or push your changes into your public Git tree). Multiple commits
for your branch should be squash'ed into a single commit. This can be done
one of two ways.

Way One: Push your changes to the github repository.

  git push origin my-develop

Then go to https://github.com/obgm/libcoap/pulls and create your pull request
for others to review.  If changes are needed, then commit and squash changes
and push the (forced) changes again.

  git push -f origin my-develop

Way Two: Post your patch series on the mailing list so other contributors
will see your work and give further suggestions or discuss your work.

To be able to send a patch series you will now create the series itself as
single patches, this will be going easy with the 'git format-patch' command
against the 'develop' branch, remember this is the upstream primary
development branch.

To not mix up your series with probably unrelated patches let git place the
patches within a defined directory. Also, while create the patches, tell git to
create a cover letter patch so you can append some introducing words that will
hold probably explanations why you create the patches in the way you have done.

  git format-patch --cover-letter -o ../patches4libcoap

This command will create a patch series in ../patches4libcoap where you find a
patch named '0000-cover-letter.patch'. Please modify this patch with some
useful information's for the mailing list. After finish this you now can send
your <NAME_EMAIL>

  git send-email ../patches4libcoap/* --to=<EMAIL>

5. Coding rules
~~~~~~~~~~~~~~~
As every FOSS project the libcoap project needs also some rules for coding.
There are lots but the main ones following are important!

5.1 pre-commit
--------------
pre-commit is used to check the the syntax of *.c and *.h files according to
the libcoap coding rules. The files are checked on github for every 'git push',
and can be locally checked if pre-commit is installed for every 'git commit'.

$ cd libcoap
$ pip install pre-commit
$ pre-commit install --allow-missing-config

5.2 License and Copyright
-------------------------
Every new file must contain a license and the copyright holder(s). Please
take a look into existing files and adopt the needed changes to your new
file(s).

5.3 Source Code Indentation
---------------------------
* For better reading the indentation is set to 2 characters as spaces, this
  is depended on the often used nested functions like 'if-else'. Don't use
  TABs any where (apart from Makefile's indenting where TABs are mandatory)!
  Avoid trailing white spaces at the end of a line.
  It's appropriate to set up a modline like this one at first line within
  the source file:

--8<----
/* -*- Mode: C; tab-width: 2; indent-tabs-mode: nil; c-basic-offset: 2 -*- */
--->8--

* For functions defined in *.h files, the function return type should be
  defined on the same line as the function name declaration. For functions
  defined in *.c files, the function return type should be defined on the
  previous line (for both declarations and implementations).

* Single lines within the source code should not be longer then 78
  characters.

* If there are functions with a lot of parameters that do not fit into the above
  rule split the declaration (in the *.h) and the implementation (in the *.c)
  into single lines per parameter. Each parameter should be aligned with the
  first defined parameter.  For example like this (from src/coap_block.c):

--8<----
int
coap_add_block(coap_pdu_t *pdu,
               unsigned int len,
               const unsigned char *data,
               unsigned int block_num,
               unsigned char block_szx) {
--->8--

5.4 Source Code Documentation
-----------------------------
* A useful source code documentation is mandatory. Mostly to be done within the
  source code files, but more complex description should be done in extra
  README files.

* Please set up/adjust the doxygen documentation if you create new functions or
  change existing functions. The doxygen documentation has to be done in the
  header files as they are the public part of the libcoap and only use the
  @-syntax for doxygen commands (akin to javadoc).

* There are also man(1) pages to document how to use the APIs, held in the man/
  directory. Any API changes or new APIs should be documented here to aid
  development for other users.

5.5 API Changes
---------------
* Never break the API!
  Don't remove old functions and if there some changes are needed in some kind
  always provide a wrapper for the old call to let the library be backward
  compatible and mark the old function as @deprecated in the doxygen comment.
  Please discuss needed changes on the mailing list.

5.6 Patches and Commits
-----------------------
* Git commits must be atomic and contain a declarative subject line (max 50
  characters if possible) and a body for a statement if needed.
  Use the opportunity to write a good explanation why your patch/commit is to
  handle the changes in the way you have done. Remember that other users can
  read your code but not necessary understand why the code is written this
  way. Don't use something to generic like "bugfix commit".

* A patch/commit or a series of patches/commits have to ensure that the
  whole project is able to build up every thing, in short: Do not break
  any make target and test your work.

* Every patch/commit should handle one single logical change. If more than
  one patch/commit is needed for a change explain it, respect the point
  above. If your subject line become much larger than 50 characters then
  typically your patch is to big for one single commit.

* Commit message should begin with a submodule or unit the commit is for.
  Doing this in your commit message helps to find thematic other changes. If
  you have to search and find something with 'git log | grep [foo]' you will
  see why this is useful. Examples:

    rd.c: Fixed type-specifier warning
    Makefile.am: Added missing src/coap_address.c
    address.[hc]: make coap_address_equals() not inline on POSIX

6. Where to start contributing?
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
There are various things you could starting on to contribute, the best
is you simply pick up an issue you can easily see and just improve the
situation. Please take also a look into the file TODO and choose a point
from there or point the maintainer to add other things here too.

* Documentation
We always need better documentation on the source code, as well as improving
the doxygen documentation in the header files.  Updating the man pages for
missing functions helps.
Also updated documentation on the usage of the libcoap and the example
binaries is always useful. So we appreciate any help on this.

* Manual Pages for example binaries
Manual pages are provided for the example binaries which have become more
complex over time with a variety of configuration options.  It is possible
to work out how to use particular application functionality by reading the
examples source code. Improving documentation for usage would be useful.

* Manual Pages for public API
As well as the doxygen documentation, manual pages are being written for the
public licoap API where functions are defined, what the parameters mean as
well as providing simple coding examples.  These manual pages are also
embedded in the doxygen output, the latest copy of which can be seen at
https://libcoap.net/doc/reference/develop/
Updating these manual pages for the missing functions, correcting errors
or providing simple examples of function usage would be helpful.

* HowTo's
The libcoap library has now a lot of functions you can use.
Unfortunately there is no good user guide on how to use the libcoap in
any external project. This means there is no HowTo or CheatSheet for a
programming person available. Do you want to write up something?

* Missing functionality
There are some features that are still missing inside the libcoap. For
example some DTLS implementations and proxy functionality.
