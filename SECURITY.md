# Security Policy

## No Warranty

Per the terms of the BSD-2-Clause license, libcoap is offered "as is" and
without any guarantee or warranty pertaining to its operation. While every
reasonable effort is made by its maintainers to ensure the product remains
free of security vulnerabilities, users are ultimately responsible for
conducting their own evaluations of each software release.

## Reporting a Suspected Vulnerability

If you believe you've uncovered a security vulnerability and wish to report
it confidentially, you may do so via email. Please note that any reported
vulnerabilities **MUST** meet all the following conditions:

* Affects the most recent stable release of libcoap, or a current beta release
* Is reproducible following a prescribed set of instructions

Please note that we **DO NOT** accept reports generated by automated tooling
which merely suggest that a file or file(s) _may_ be vulnerable under certain
conditions, as these are most often innocuous.

If you believe that you've found a vulnerability which meets all of these
conditions, please email a brief description of the suspected bug and
instructions for reproduction to **<EMAIL>**. Please do NOT
create a public GitHub issue.

### Bug Bounties

As libcoap is provided as free open source software, we do not offer any monetary
compensation for vulnerability or bug reports, however your contributions are greatly
appreciated.
