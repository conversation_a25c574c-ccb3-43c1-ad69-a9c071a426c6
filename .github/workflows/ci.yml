name: <PERSON>I Checks

on:
  push:
    branches: ["**"]
  pull_request:
    branches: [main]
  workflow_dispatch:

jobs:
  unittest:
    runs-on: ubuntu-latest
    steps:
      - name: Clone This Repo
        uses: actions/checkout@v2
      - name: Build
        run: |
          sudo apt-get install -y lcov
          cmake -S test -B build/ \
          -G "Unix Makefiles" \
          -DCMAKE_BUILD_TYPE=Debug \
          -DBUILD_CLONE_SUBMODULES=ON \
          -DCMAKE_C_FLAGS='--coverage -Wall -Wextra -Werror -DNDEBUG -DLIBRARY_LOG_LEVEL=LOG_DEBUG'
          make -C build/ all
      - name: Test
        run: |
          cd build/
          ctest -E system --output-on-failure
          cd ..
      - name: Run Coverage
        run: |
          make -C build/ coverage
          declare -a EXCLUDE=("\*test\*" "\*CMakeCCompilerId\*" "\*mocks\*")
          echo ${EXCLUDE[@]} | xargs lcov --rc lcov_branch_coverage=1 -r build/coverage.info -o build/coverage.info
          lcov --rc lcov_branch_coverage=1 --list build/coverage.info
      - name: Check Coverage
        uses: FreeRTOS/CI-CD-Github-Actions/coverage-cop@main
        with:
          path: ./build/coverage.info
  complexity:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Check complexity
        uses: FreeRTOS/CI-CD-Github-Actions/complexity@main
        with:
          path: ./
          horrid_threshold: 10
  doxygen:
    runs-on: ubuntu-20.04
    steps:
      - uses: actions/checkout@v2
      - name: Run doxygen build
        uses: FreeRTOS/CI-CD-Github-Actions/doxygen@main
        with:
          path: ./
  spell-check:
    runs-on: ubuntu-latest
    steps:
      - name: Clone This Repo
        uses: actions/checkout@v2
      - name: Run spellings check
        uses: FreeRTOS/CI-CD-Github-Actions/spellings@main
        with:
          path: ./
  formatting:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Check formatting
        uses: FreeRTOS/CI-CD-Github-Actions/formatting@main
        with:
          path: ./
  git-secrets:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Checkout awslabs/git-secrets
        uses: actions/checkout@v2
        with:
          repository: awslabs/git-secrets
          ref: master
          path: git-secrets
      - name: Install git-secrets
        run: cd git-secrets && sudo make install && cd ..
      - name: Run git-secrets
        run: |
          git-secrets --register-aws
          git-secrets --scan
  custom-standard-c-headers:
    runs-on: ubuntu-latest
    steps:
      - name: Clone This Repo
        uses: actions/checkout@v2
      - name: Build
        run: |
          mkdir -p override-include
          cp source/include/stdbool.readme override-include/stdbool.h
          cp source/include/stdint.readme override-include/stdint.h
          cmake -S test -B build/ \
          -G "Unix Makefiles" \
          -DBUILD_CLONE_SUBMODULES=ON \
          -DCMAKE_C_FLAGS='-Wall -Wextra -I../override-include'
          make -C build/ coverity_analysis
  memory_statistics:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
        with:
            submodules: 'recursive'
      - name: Install Python3
        uses: actions/setup-python@v2
        with:
          python-version: '3.7.10'
      - name: Measure sizes
        uses: FreeRTOS/CI-CD-Github-Actions/memory_statistics@main
        with:
            config: .github/memory_statistics_config.json
            check_against: docs/doxygen/include/size_table.md
