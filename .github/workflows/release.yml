name: Release automation

on:
  workflow_dispatch:
    inputs:
      commit_id:
        description: 'Commit ID to tag and create a release for'
        required: true
      version_number:
        description: 'Release Version Number (Eg, v1.0.0)'
        required: true
      delete_existing_tag_release:
        description: 'Is this a re-release of existing tag/release? (Default: false)'
        default: 'false'
        required: false
jobs:
  clean-existing-tag-and-release:
    if: ${{ github.event.inputs.delete_existing_tag_release == 'true' }}
    runs-on: ubuntu-latest
    env:
      VERSION_NUM: ${{ github.event.inputs.version_number }}
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v2
      - name: Check if tag exists
        run: |
          git fetch origin
          if git tag --list $VERSION_NUM
          then
              echo "Deleting existing tag for $VERSION_NUM"
              git push origin --delete tags/$VERSION_NUM
          fi
      - name: Check if release exists
        run: |
          sudo apt-key adv --keyserver keyserver.ubuntu.com --recv-key 23F3D4EA75716059
          sudo apt-add-repository https://cli.github.com/packages
          sudo apt update
          sudo apt-get install gh
          if gh release list | grep $VERSION_NUM
          then
              echo "Deleting existing release for $VERSION_NUM"
              gh release delete --yes $VERSION_NUM
          fi
  add-sbom-and-tag-commit:
    if: ${{ ( github.event.inputs.delete_existing_tag_release == 'true' && success() )  || ( github.event.inputs.delete_existing_tag_release == 'false' && always() ) }}
    needs: clean-existing-tag-and-release
    name: Tag commit
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v2
        with:
          ref: ${{ github.event.inputs.commit_id }}
      - name: Configure git identity
        run: |
          git config --global user.name ${{ github.actor }}
          git config --global user.email ${{ github.actor }}@users.noreply.github.com
      - name: create a new branch that references commit id
        run: git checkout -b ${{ github.event.inputs.version_number }} ${{ github.event.inputs.commit_id }}
      - name: Generate SBOM
        uses: FreeRTOS/CI-CD-Github-Actions/sbom-generator@main
        with:
          repo_path: ./
          source_path: ./source
      - name: commit SBOM file
        run: |
          git add .
          git commit -m 'Update SBOM'
          git push -u origin ${{ github.event.inputs.version_number }}
      - name: Tag Commit and Push to remote
        run: |
          git tag ${{ github.event.inputs.version_number }} -a -m "coreMQTT Library ${{ github.event.inputs.version_number }}"
          git push origin --tags
      - name: Verify tag on remote
        run: |
          git tag -d ${{ github.event.inputs.version_number }}
          git remote update
          git checkout tags/${{ github.event.inputs.version_number }}
          git diff ${{ github.event.inputs.commit_id }} tags/${{ github.event.inputs.version_number }}
  create-zip:
    if: ${{ ( github.event.inputs.delete_existing_tag_release == 'true' && success() )  || ( github.event.inputs.delete_existing_tag_release == 'false' && always() ) }}
    needs: add-sbom-and-tag-commit
    name: Create ZIP and verify package for release asset.
    runs-on: ubuntu-latest
    steps:
      - name: Install ZIP tools
        run: sudo apt-get install zip unzip
      - name: Checkout code
        uses: actions/checkout@v2
        with:
          ref: ${{ github.event.inputs.commit_id }}
          path: coreMQTT
          submodules: recursive
      - name: Checkout disabled submodules
        run: |
          cd coreMQTT
          git submodule update --init --checkout --recursive
      - name: Create ZIP
        run: |
          zip -r coreMQTT-${{ github.event.inputs.version_number }}.zip coreMQTT -x "*.git*"
          ls ./
      - name: Validate created ZIP
        run: |
          mkdir zip-check
          mv coreMQTT-${{ github.event.inputs.version_number }}.zip zip-check
          cd zip-check
          unzip coreMQTT-${{ github.event.inputs.version_number }}.zip -d coreMQTT-${{ github.event.inputs.version_number }}
          ls coreMQTT-${{ github.event.inputs.version_number }}
          diff -r -x "*.git*" coreMQTT-${{ github.event.inputs.version_number }}/coreMQTT/ ../coreMQTT/
          cd ../
      - name: Build
        run: |
          cd zip-check/coreMQTT-${{ github.event.inputs.version_number }}/coreMQTT
          sudo apt-get install -y lcov
          cmake -S test -B build/ \
          -G "Unix Makefiles" \
          -DCMAKE_BUILD_TYPE=Debug \
          -DBUILD_CLONE_SUBMODULES=ON \
          -DCMAKE_C_FLAGS='--coverage -Wall -Wextra -Werror -DNDEBUG'
          make -C build/ all
      - name: Test
        run: |
          cd zip-check/coreMQTT-${{ github.event.inputs.version_number }}/coreMQTT/build/
          ctest -E system --output-on-failure
          cd ..
      - name: Create artifact of ZIP
        uses: actions/upload-artifact@v2
        with:
          name: coreMQTT-${{ github.event.inputs.version_number }}.zip
          path: zip-check/coreMQTT-${{ github.event.inputs.version_number }}.zip
  deploy-doxygen:
    needs: add-sbom-and-tag-commit
    if: ${{ ( github.event.inputs.delete_existing_tag_release == 'true' && success() )  || ( github.event.inputs.delete_existing_tag_release == 'false' && always() ) }}
    name: Deploy doxygen documentation
    runs-on: ubuntu-latest
    steps:
      - name: Doxygen generation
        uses: FreeRTOS/CI-CD-Github-Actions/doxygen-generation@main
        with:
          ref: ${{ github.event.inputs.version_number }}
          add_release: "true"
  create-release:
    needs:
      - create-zip
      - deploy-doxygen
    if: ${{ ( github.event.inputs.delete_existing_tag_release == 'true' && success() )  || ( github.event.inputs.delete_existing_tag_release == 'false' && always() ) }}
    name: Create Release and Upload Release Asset
    runs-on: ubuntu-latest
    steps:
      - name: Create Release
        id: create_release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.event.inputs.version_number }}
          release_name: ${{ github.event.inputs.version_number }}
          body: Release ${{ github.event.inputs.version_number }} of the coreMQTT Library.
          draft: false
          prerelease: false
      - name: Download ZIP artifact
        uses: actions/download-artifact@v2
        with:
          name: coreMQTT-${{ github.event.inputs.version_number }}.zip
      - name: Upload Release Asset
        id: upload-release-asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }}
          asset_path: ./coreMQTT-${{ github.event.inputs.version_number }}.zip
          asset_name: coreMQTT-${{ github.event.inputs.version_number }}.zip
          asset_content_type: application/zip
