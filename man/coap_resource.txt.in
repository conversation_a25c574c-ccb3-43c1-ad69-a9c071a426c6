// -*- mode:doc; -*-
// vim: set syntax=asciidoc tw=0

coap_resource(3)
=================
:doctype: manpage
:man source:   coap_resource
:man version:  @PACKAGE_VERSION@
:man manual:   libcoap Manual

NAME
----
coap_resource,
coap_resource_init,
coap_resource_unknown_init,
coap_resource_unknown_init2,
coap_resource_proxy_uri_init,
coap_resource_proxy_uri_init2,
coap_resource_reverse_proxy_init,
coap_add_resource,
coap_delete_resource,
coap_resource_set_mode,
coap_resource_set_userdata,
coap_resource_get_userdata,
coap_resource_release_userdata_handler,
coap_resource_get_uri_path,
coap_get_resource_from_uri_path,
coap_print_wellknown
- Work with CoAP resources

SYNOPSIS
--------
*#include <coap@LIBCOAP_API_VERSION@/coap.h>*

*coap_resource_t *coap_resource_init(coap_str_const_t *_uri_path_,
int _flags_);*

*coap_resource_t *coap_resource_unknown_init(coap_method_handler_t
_put_handler_);*

*coap_resource_t *coap_resource_unknown_init2(coap_method_handler_t
_put_handler_, int _flags_);*

*coap_resource_t *coap_resource_proxy_uri_init(coap_method_handler_t
_proxy_handler_, size_t _host_name_count_, const char *_host_name_list_[]);*

*coap_resource_t *coap_resource_proxy_uri_init2(coap_method_handler_t
_proxy_handler_, size_t _host_name_count_, const char *_host_name_list_[],
int _flags_);*

*coap_resource_t *coap_resource_reverse_proxy_init(coap_method_handler_t
_rev_proxy_handler_, int _flags_);*

*void coap_add_resource(coap_context_t *_context_,
coap_resource_t *_resource_);*

*int coap_delete_resource(coap_context_t *_context_,
coap_resource_t *_resource_);*

*void coap_resource_set_mode(coap_resource_t *_resource_, int _mode_);*

*void coap_resource_set_userdata(coap_resource_t *_resource_, void *_data_);*

*void *coap_resource_get_userdata(coap_resource_t *_resource_);*

*void coap_resource_release_userdata_handler(coap_context_t *_context_,
coap_resource_release_userdata_handler_t _callback_);*

*coap_str_const_t *coap_resource_get_uri_path(coap_resource_t *_resource_);*

*coap_resource_t *coap_get_resource_from_uri_path(coap_context_t *_context_,
coap_str_const_t *_uri_path_);*

*coap_print_status_t coap_print_wellknown(coap_context_t *context,
unsigned char *buf, size_t *buflen, size_t offset,
const coap_string_t *query_filter);*

For specific (D)TLS library support, link with
*-lcoap-@LIBCOAP_API_VERSION@-notls*, *-lcoap-@LIBCOAP_API_VERSION@-gnutls*,
*-lcoap-@LIBCOAP_API_VERSION@-openssl*, *-lcoap-@LIBCOAP_API_VERSION@-mbedtls*,
*-lcoap-@LIBCOAP_API_VERSION@-wolfssl*
or *-lcoap-@LIBCOAP_API_VERSION@-tinydtls*.   Otherwise, link with
*-lcoap-@LIBCOAP_API_VERSION@* to get the default (D)TLS library support.

DESCRIPTION
-----------
CoAP Resources on a CoAP Server need to be created, updated and deleted as
appropriate. The URI in
the request packet defines the resource to work with, with possibly the Query
or data in the request referring to a sub-resource.

When resources are configured on the CoAP server, the URI to match against
in the request packet is specified.

Callback Handlers are then added to the resource to handle the different
request methods. See *coap_register_request_handler*(3) for further information.

Adding Attributes allows textual information to be added to the resource
which can then be reported back to any client doing a "GET .well-known/core"
request. See *coap_add_attr*(3) for further information.

If an incoming packet request matches a resource's URI and Method, then
the appropriate callback resource handler is invoked to process the packet
which should then update a suitable response packet for returning back to the
requester.

There is support for handling incoming packets where the URI is unknown (no
specific resource has been created).
This could, for example, happen when a PUT request is trying to create a new
resource. It is the responsibility of the unknown resource callback handler
to either create a new resource for the URI or just manage things separately.

CoAP Observe (https://rfc-editor.org/rfc/rfc7641[RFC7641]) is not supported
for unknown resources, so a new
resource with GET handler must be created by the unknown resource callback
handle matching the URI which then can be Observable.

There is support for handling incoming proxy based requests using the Proxy-Uri
or Proxy-Scheme options.

FUNCTIONS
---------

*Function: coap_resource_init()*

The *coap_resource_init*() function returns a newly created _resource_ of
type _coap_resource_t_ * .  _uri_path_ specifies the uri string path to match
against.  Normally there is no need for the leading '/' - e.g. just
"full/path/for/resource".
_flags_ can be zero or more of the following definitions or'ed together.

[horizontal]
*COAP_RESOURCE_FLAGS_NOTIFY_NON*::
Set the notification message type to non-confirmable for any trigggered
"observe" responses with type set to confirmable every 5 packets as required by
"https://rfc-editor.org/rfc/rfc7641#section-4.5[RFC7641 4.5. Transmission]".
See *coap_observe*(3). +
*NOTE:* This flag is ignored if COAP_RESOURCE_FLAGS_NOTIFY_CON is set.

*COAP_RESOURCE_FLAGS_NOTIFY_NON_ALWAYS*::
Set the notification message type to always non-confirmable for any trigggered
"observe" responses. This should only be used if a upper layer protocol
requires it. See *coap_observe*(3). +
*NOTE:* This flag is ignored if COAP_RESOURCE_FLAGS_NOTIFY_CON is set.

*COAP_RESOURCE_FLAGS_NOTIFY_CON*::
Set the notification message type to confirmable for any trigggered
"observe" responses. See *coap_observe*(3). +
*NOTE:* COAP_RESOURCE_FLAGS_NOTIFY_NON is assumed if
COAP_RESOURCE_FLAGS_NOTIFY_CON is not set.

*COAP_RESOURCE_FLAGS_RELEASE_URI*::
Free off the coap_str_const_t for _uri_path_ when the _resource_ is deleted.

*COAP_RESOURCE_FLAGS_FORCE_SINGLE_BODY*::
Force all large traffic to this resource to be presented as a single body
to the request handler.

*COAP_RESOURCE_FLAGS_OSCORE_ONLY*::
Define this resource as an OSCORE enabled access only.

*COAP_RESOURCE_HANDLE_WELLKNOWN_CORE*::
Define this when invoking *coap_resource_unknown_init2*() if .well-known/core
is to be passed to the unknown URI handler rather than processed locally.
Used for easily passing on a request as a reverse-proxy request.

*NOTE:* The following flags are only tested against if
*coap_mcast_per_resource*() has been called.  If *coap_mcast_per_resource*()
has not been called, then all resources have multicast support, libcoap adds
in random delays to the responses, and 4.xx / 5.xx responses are dropped.

*NOTE:* The pseudo resource for ".well-known/core" always has multicast
support enabled and is not configurable. It is possible for a server to
create a resource for ".well-known/core" that can then control the
multicast support and the provided GET request handler can call
*coap_print_wellknown*() to produce the same information as the pseudo
resource.

[horizontal]
*COAP_RESOURCE_FLAGS_HAS_MCAST_SUPPORT*::
This resource has support for multicast requests.

*COAP_RESOURCE_FLAGS_LIB_DIS_MCAST_DELAYS*::
Disable libcoap library from adding in delays to multicast requests before
sending the response back to the client.  It is then the responsibility of
the app to delay the responses for multicast requests. See
"https://rfc-editor.org/rfc/rfc7252#section-8.2[RFC7252 8.2. Request/Response
Layer]".
However, the pseudo resource for ".well-known/core" always has multicast
support enabled.

*COAP_RESOURCE_FLAGS_LIB_ENA_MCAST_SUPPRESS_2_05*::
Enable libcoap library suppression of 205 multicast responses that are empty
(overridden by https://rfc-editor.org/rfc/rfc7967[RFC7967] No-Response option)
for multicast requests.

*COAP_RESOURCE_FLAGS_LIB_ENA_MCAST_SUPPRESS_2_XX*::
Enable libcoap library suppressing 2.xx multicast responses (overridden by
https://rfc-editor.org/rfc/rfc7967[RFC7967] No-Response option) for multicast
requests.

*COAP_RESOURCE_FLAGS_LIB_DIS_MCAST_SUPPRESS_4_XX*::
Disable libcoap library suppressing 4.xx multicast responses (overridden by
https://rfc-editor.org/rfc/rfc7967[RFC7967] No-Response option) for multicast
requests.

*COAP_RESOURCE_FLAGS_LIB_DIS_MCAST_SUPPRESS_5_XX*::
Disable libcoap library suppressing 5.xx multicast responses (overridden by
https://rfc-editor.org/rfc/rfc7967[RFC7967] No-Response option) for multicast
requests.

*NOTE:* _uri_path_, if not 7 bit readable ASCII, binary bytes must be hex
encoded according to the rules defined in
"https://rfc-editor.org/rfc/rfc3986#section-2.1[RFC3986 2.1. Percent-Encoding]".

*Function: coap_resource_unknown_init()*

The *coap_resource_unknown_init*() function returns a newly created _resource_
of type _coap_resource_t_ *. _put_handler_ is automatically added to the
_resource_ to handle PUT requests to resources that are unknown. Additional
handlers can be added to this resource if required.

*Function: coap_resource_unknown_init2()*

The *coap_resource_unknown_init2*() function returns a newly created _resource_
of type _coap_resource_t_ *. _put_handler_ is automatically added to the
_resource_ to handle PUT requests to resources that are unknown. Additional
handlers can be added to this resource if required. _flags_ can be zero or
more of the COAP_RESOURCE_FLAGS MCAST definitions.

*NOTE:* There can only be one reverse-proxy or unknown resource handler per
context - attaching a new one overrides the previous definition.

*Function: coap_resource_proxy_uri_init()*

The *coap_resource_proxy_uri_init*() function returns a newly created
_resource_ of type _coap_resource_t_ *. _proxy_handler_ is automatically added
to the _resource_ to handle PUT/POST/GET etc. requests that use the Proxy-Uri
option.  There is no need to add explicit request type handlers. One or more
names by which the proxy is known by (IP address, DNS name etc.) must be
supplied in the array defined by _host_name_list_[] which has a count of
_host_name_count_.  This is used to check whether the current endpoint is
the proxy target address, or the request has to be passed on to an upstream
server.

*Function: coap_resource_proxy_uri_init2()*

The *coap_resource_proxy_uri_init2*() function returns a newly created
_resource_ of type _coap_resource_t_ *. _proxy_handler_ is automatically added
to the _resource_ to handle PUT/POST/GET etc. requests that use the Proxy-Uri
option.  There is no need to add explicit request type handlers. One or more
names by which the proxy is known by (IP address, DNS name etc.) must be
supplied in the array defined by _host_name_list_[] which has a count of
_host_name_count_.  This is used to check whether the current endpoint is
the proxy target address, or the request has to be passed on to an upstream
server. _flags_ can be zero or more COAP_RESOURCE_FLAGS MCAST definitions.

*NOTE:* There can only be one proxy resource handler per
context - attaching a new one overrides the previous definition.

*Function: coap_resource_reverse_proxy_init()*

The *coap_resource_reverse_proxy_init*() function returns a newly created
_resource_ of type _coap_resource_t_ *. _rev_proxy_handler_ is automatically added
to the _resource_ to handle all the PUT/POST/GET etc. requests including
.well-known/core. There is no need to add explicit request type handlers.
_flags_ can be zero or more COAP_RESOURCE_FLAGS MCAST definitions.

*NOTE:* There can only be one reverse-proxy or unknown resource handler per
context - attaching a new one overrides the previous definition.

*Function: coap_add_resource()*

The *coap_add_resource*() function registers the given _resource_ with the
_context_. The _resource_ must have been created by *coap_resource_init*(),
*coap_resource_unknown_init*(), *coap_resource_unknown_init2*(),
*coap_resource_proxy_uri_init*() or *coap_resource_proxy_uri_init2*(). The
storage allocated for the _resource_ will be released by
*coap_delete_resource*().

As the _uri_path_ of the resource has to be unique across all of the resources
associated with a _context_, *coap_add_resource*() will delete any previous
_resource_ with the same _uri_path_ before adding in the new _resource_.

*Function: coap_delete_resource()*

The *coap_delete_resource*() function deletes the resource identified by
_resource_. The _context_ parameter is ignored. The storage allocated for that
_resource_ is freed, along with any attributes associated with the _resource_.

*Function: coap_resource_set_mode()*

The *coap_resource_set_mode*() changes the unsolicited notification message
type of _resource_ to the given _mode_ which must be one of
COAP_RESOURCE_FLAGS_NOTIFY_NON, COAP_RESOURCE_FLAGS_NOTIFY_NON_ALWAYS or
COAP_RESOURCE_FLAGS_NOTIFY_CON.

*Function: coap_resource_set_userdata()*

The *coap_resource_set_userdata*() function allows a pointer to user _data_
to be associated with a _resource_ that can accessed in any callback that
includes _resource_ as a parameter.

*NOTE:* _data_ must point to a static, or allocated, block of memory.

*Function: coap_resource_get_userdata()*

The *coap_resource_get_userdata*() function obtains the user data pointer
from the _resource_ that had previously been set up by
*coap_resource_set_userdata*().

*Function: coap_resource_release_userdata_handler()*

The *coap_resource_release_userdata_handler*() function defines the _context_
wide _callback_ handler to call to release the allocated user data that has
been added to the resource using *coap_resource_set_userdata*() when the
resource is deleted. _callback_ can be NULL (which is the default) if nothing
needs to be freed off.

*Function: coap_resource_get_uri_path()*

The *coap_resource_get_uri_path*() function is used to obtain the UriPath of
the _resource_ definion.

*Function: coap_get_resource_from_uri_path()*

The *coap_get_resource_from_uri_path*() function is used to return the resource
identified by the unique string _uri_path_ associated with _context_.

*Function: coap_print_wellknown()*

The *coap_print_wellknown*() function prints the names of all known resources
of the given _context_ into _buf_ which has a maximum size of _buflen_. The
first _offset_ bytes are skipped from the output to handle block transfers.
Setting _offset_ to 0 means the entire (matching) information is output.
Setting _offset_ to UINT_MAX skips, but calculates the size of, the (matching)
output.
The _query_filter_ is usually defined by the CoAP Uri-Query options as a query.

RETURN VALUES
-------------
*coap_resource_init*(), *coap_resource_unknown_init*(),
*coap_resource_unknown_init2*(), *coap_resource_proxy_uri_init*(),
*coap_resource_proxy_uri_init2*() and *coap_resource_reverse_proxy_init*()
return a newly created resource or NULL if there is a malloc failure.

*coap_delete_resource*() returns 0 on failure (_resource_ not
found), 1 on success.

*coap_resource_get_userdata*() returns the value previously set
by the *coap_resource_set_userdata*() function or NULL.

*coap_resource_get_uri_path*() returns the uri_path or NULL if
there was a failure.

*coap_get_resource_from_uri_path*() returns the resource or NULL
if not found.

*coap_print_wellknown*() returns COAP_PRINT_STATUS_ERROR on error. Otherwise,
the lower 28 bits are set to the number of bytes that have actually been
written. COAP_PRINT_STATUS_TRUNC is set when the output has been truncated.

EXAMPLES
--------
*Fixed Resources Set Up*

[source, c]
----
#include <coap@LIBCOAP_API_VERSION@/coap.h>

#define INDEX "This is an example server using libcoap\n"

static void
hnd_get_index(coap_resource_t *resource, coap_session_t *session,
              const coap_pdu_t *request, const coap_string_t *query,
              coap_pdu_t *response) {
  unsigned char buf[3];
  /* Remove (void) definition if variable is used */
  (void)resource;
  (void)session;
  (void)request;
  (void)query;

  coap_pdu_set_code(response, COAP_RESPONSE_CODE_CONTENT);

  coap_add_option(response,
                  COAP_OPTION_CONTENT_TYPE,
                  coap_encode_var_safe(buf, sizeof(buf),
                                       COAP_MEDIATYPE_TEXT_PLAIN),
                  buf);

  coap_add_option(response,
                  COAP_OPTION_MAXAGE,
                  coap_encode_var_safe(buf, sizeof(buf), 0x2ffff), buf);

  coap_add_data(response, strlen(INDEX), (const uint8_t *)INDEX);

  coap_pdu_set_code(response, COAP_RESPONSE_CODE_CONTENT);
}

static void
hnd_delete_time(coap_resource_t *resource, coap_session_t *session,
                const coap_pdu_t *request, const coap_string_t *query,
                coap_pdu_t *response) {
  /* Remove (void) definition if variable is used */
  (void)resource;
  (void)session;
  (void)request;
  (void)query;

  /* .. code .. */

  coap_pdu_set_code(response, COAP_RESPONSE_CODE_DELETED);
}

static void
hnd_get_time(coap_resource_t *resource, coap_session_t *session,
             const coap_pdu_t *request, const coap_string_t *query,
             coap_pdu_t *response) {
  /* Remove (void) definition if variable is used */
  (void)resource;
  (void)session;
  (void)request;
  (void)query;
  (void)response;

  /* .. code .. */

  coap_pdu_set_code(response, COAP_RESPONSE_CODE_CONTENT);
}

static void
hnd_put_time(coap_resource_t *resource, coap_session_t *session,
             const coap_pdu_t *request, const coap_string_t *query,
             coap_pdu_t *response) {
  /* Remove (void) definition if variable is used */
  (void)resource;
  (void)session;
  (void)request;
  (void)query;
  (void)response;

  /* .. code .. */

  coap_pdu_set_code(response, COAP_RESPONSE_CODE_CHANGED);
}

static void
init_resources(coap_context_t *ctx) {

  coap_resource_t *r;

  /* Create a resource to return general information */
  r = coap_resource_init(NULL, 0);
  coap_register_request_handler(r, COAP_REQUEST_GET, hnd_get_index);

  /* Document resource for '.well-known/core' request */
  coap_add_attr(r, coap_make_str_const("ct"), coap_make_str_const("0"), 0);
  coap_add_attr(r, coap_make_str_const("title"),
                coap_make_str_const("\"General Info\""), 0);

  coap_add_resource(ctx, r);

  /* Create a resource to return return or update time */
  r = coap_resource_init(coap_make_str_const("time"),
                         COAP_RESOURCE_FLAGS_NOTIFY_CON);
  coap_resource_set_get_observable(r, 1);
  coap_register_request_handler(r, COAP_REQUEST_GET, hnd_get_time);
  coap_register_request_handler(r, COAP_REQUEST_PUT, hnd_put_time);
  coap_register_request_handler(r, COAP_REQUEST_DELETE, hnd_delete_time);

  /* Document resource for 'time' request */
  coap_add_attr(r, coap_make_str_const("ct"), coap_make_str_const("0"), 0);
  coap_add_attr(r, coap_make_str_const("title"),
                coap_make_str_const("\"Internal Clock\""), 0);
  coap_add_attr(r, coap_make_str_const("rt"), coap_make_str_const("\"secs\""),
                0);
  coap_add_attr(r, coap_make_str_const("if"), coap_make_str_const("\"clock\""),
                0);

  coap_add_resource(ctx, r);

}
----

*Dynamic Resources Set Up*

[source, c]
----
#include <coap@LIBCOAP_API_VERSION@/coap.h>

/* Regular DELETE handler - used by resources created by the
 * Unknown Resource PUT handler */

static void
hnd_delete(coap_resource_t *resource, coap_session_t *session,
           const coap_pdu_t *request, const coap_string_t *query,
           coap_pdu_t *response) {
  /* Remove (void) definition if variable is used */
  (void)session;
  (void)request;
  (void)query;
  (void)response;

  /* .. code .. */

  /* Dynamic resource no longer required - delete it */
  coap_delete_resource(NULL, resource);

  coap_pdu_set_code(response, COAP_RESPONSE_CODE_DELETED);
}

/* Regular GET handler - used by resources created by the
 * Unknown Resource PUT handler */

static void
hnd_get(coap_resource_t *resource, coap_session_t *session,
        const coap_pdu_t *request, const coap_string_t *query,
        coap_pdu_t *response) {
  coap_str_const_t *get_uri_path;

  /* Remove (void) definition if variable is used */
  (void)resource;
  (void)session;
  (void)request;
  (void)query;

  /*
   * Get the specific resource being requested to determine what the response is
   * The uri_path string is a const pointer
   */

  get_uri_path = coap_resource_get_uri_path(resource);

  /* .. code .. */

  coap_pdu_set_code(response, COAP_RESPONSE_CODE_CONTENT);
}

/* Regular PUT handler - used by resources created by the
 * Unknown Resource PUT handler */

static void
hnd_put(coap_resource_t *resource, coap_session_t *session,
        const coap_pdu_t *request, const coap_string_t *query,
        coap_pdu_t *response) {
  /* Remove (void) definition if variable is used */
  (void)resource;
  (void)session;
  (void)query;

  coap_string_t *put_uri_path;
  size_t length;
  const uint8_t *data;
  size_t offset;
  size_t total;
  int new_resource = 0;

  /* get the uri_path */
  put_uri_path = coap_get_uri_path(request);
  if (!put_uri_path) {
    coap_pdu_set_code(response, COAP_RESPONSE_CODE_NOT_FOUND);
    return;
  }
  coap_get_data_large(request, &length, &data, &offset, &total);

  /* .. code .. */

  /* Need to do this as coap_get_uri_path() created it */
  coap_delete_string(put_uri_path);

  if (length + offset < total)
    coap_pdu_set_code(response, COAP_RESPONSE_CODE_CONTINUE);
  else if (new_resource)
    coap_pdu_set_code(response, COAP_RESPONSE_CODE_CREATED);
  else
    coap_pdu_set_code(response, COAP_RESPONSE_CODE_CHANGED);
}

static int
check_url_fn(coap_string_t *uri_path, uint8_t code) {
  /* Remove (void) definition if variable is used */
  (void)uri_path;
  (void)code;

  /* Code to determine whether the uri is valid or not */

  return 1;
}

/* Unknown Resource PUT handler */

static void
hnd_put_unknown(coap_resource_t *resource, coap_session_t *session,
                const coap_pdu_t *request, const coap_string_t *query,
                coap_pdu_t *response) {
  /* Remove (void) definition if variable is used */
  (void)resource;
  coap_pdu_code_t req_code = coap_pdu_get_code(request);

  coap_resource_t *r;
  coap_string_t *uri_path;

  /* get the uri_path - which will get used by coap_resource_init() */
  uri_path = coap_get_uri_path(request);
  if (!uri_path) {
    coap_pdu_set_code(response, COAP_RESPONSE_CODE_NOT_FOUND);
    return;
  }

  /* Check if new URI Path is valid */
  if (!check_url_fn(uri_path, req_code)) {
    coap_pdu_set_code(response, COAP_RESPONSE_CODE_NOT_FOUND);
    coap_delete_string(uri_path);
    return;
  }

  /*
   * Create a resource to handle the new URI
   * uri_path will get deleted when the resource is removed
   */
  r = coap_resource_init((coap_str_const_t *)uri_path,
                         COAP_RESOURCE_FLAGS_RELEASE_URI | COAP_RESOURCE_FLAGS_NOTIFY_NON);
  coap_register_request_handler(r, COAP_REQUEST_PUT, hnd_put);
  coap_register_request_handler(r, COAP_REQUEST_DELETE, hnd_delete);
  /* We possibly want to Observe the GETs */
  coap_resource_set_get_observable(r, 1);
  coap_register_request_handler(r, COAP_REQUEST_GET, hnd_get);
  coap_add_resource(coap_session_get_context(session), r);

  /* Do the PUT for this first call */
  hnd_put(r, session, request, query, response);

  return;

}

/* Initialize single Unknown Resource PUT handler */

static void
init_resources(coap_context_t *ctx) {

  coap_resource_t *r;

  /* Create a resource to handle PUTs to unknown URIs */
  r = coap_resource_unknown_init2(hnd_put_unknown, 0);
  /*
   * Additional handlers can be added - for example
   *  coap_register_request_handler(r, COAP_REQUEST_POST, hnd_post_unknown);
   *  coap_register_request_handler(r, COAP_REQUEST_GET, hnd_get_unknown);
   *  coap_register_request_handler(r, COAP_REQUEST_DELETE, hnd_delete_unknown);
   */
  coap_add_resource(ctx, r);

}
----

SEE ALSO
--------
*coap_attribute*(3), *coap_context*(3), *coap_handler*(3) and *coap_observe*(3)

FURTHER INFORMATION
-------------------
See

"https://rfc-editor.org/rfc/rfc7252[RFC7252: The Constrained Application Protocol (CoAP)]"

"https://rfc-editor.org/rfc/rfc3986[RFC3986: Uniform Resource Identifier (URI): Generic Syntax]"

for further information.

BUGS
----
Please raise an issue on GitHub at
https://github.com/obgm/libcoap/issues to report any bugs.

Please raise a Pull Request at https://github.com/obgm/libcoap/pulls
for any fixes.

AUTHORS
-------
The libcoap project <<EMAIL>>
