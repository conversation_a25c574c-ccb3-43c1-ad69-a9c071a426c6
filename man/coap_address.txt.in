// -*- mode:doc; -*-
// vim: set syntax=asciidoc tw=0

coap_address(3)
===============
:doctype: manpage
:man source:   coap_address
:man version:  @PACKAGE_VERSION@
:man manual:   libcoap Manual

NAME
----
coap_address,
coap_address_t,
coap_address_init,
coap_address_copy,
coap_address_equals,
coap_address_get_port,
coap_address_set_port,
coap_get_available_scheme_hint_bits,
coap_addr_info_t,
coap_resolve_address_info,
coap_free_address_info,
coap_sockaddr_un,
coap_address_set_unix_domain,
coap_host_is_unix_domain,
coap_is_bcast,
coap_is_mcast,
coap_is_af_unix
- Work with CoAP Socket Address Types

SYNOPSIS
--------
*#include <coap@LIBCOAP_API_VERSION@/coap.h>*

*struct coap_address_t;*

*struct coap_addr_info_t;*

*struct coap_sockaddr_un;*

*void coap_address_init(coap_address_t *_addr_);*

*void coap_address_copy(coap_address_t *_dst_, const coap_address_t *_src_);*

*int coap_address_equals(const coap_address_t *_a_, const coap_address_t *_b_);*

*uint16_t coap_address_get_port(const coap_address_t *_addr_);*

*void coap_address_set_port(coap_address_t *_addr_, uint16_t _port_);*

*uint32_t coap_get_available_scheme_hint_bits(int _have_pki_psk_,
int _ws_check_, coap_proto_t _use_unix_proto_);*

*coap_addr_info_t *coap_resolve_address_info(const coap_str_const_t *_address_,
uint16_t _port_, uint16_t _secure_port_, uint16_t _ws_port_,
uint16_t _ws_secure_port_, int _ai_hints_flags_,
int _scheme_hint_bits_, coap_resolve_type_t _type_);*

*void coap_free_address_info(coap_addr_info_t *_info_list_);*

*int coap_host_is_unix_domain(const coap_str_const_t *_host_);*

*int coap_address_set_unix_domain(coap_address_t *_addr_,
const uint8_t *_host_, size_t _host_len_);*

*int coap_is_bcast(const coap_address_t *_addr_);*

*int coap_is_mcast(const coap_address_t *_addr_);*

*int coap_is_af_unix(const coap_address_t *_addr_);*

For specific (D)TLS library support, link with
*-lcoap-@LIBCOAP_API_VERSION@-notls*, *-lcoap-@LIBCOAP_API_VERSION@-gnutls*,
*-lcoap-@LIBCOAP_API_VERSION@-openssl*, *-lcoap-@LIBCOAP_API_VERSION@-mbedtls*,
*-lcoap-@LIBCOAP_API_VERSION@-wolfssl*
or *-lcoap-@LIBCOAP_API_VERSION@-tinydtls*.   Otherwise, link with
*-lcoap-@LIBCOAP_API_VERSION@* to get the default (D)TLS library support.

DESCRIPTION
-----------
This man page focuses on setting up CoAP endpoint address definitions.

*Supported Socket Types*

Libcoap supports 3 different socket types that can be used:

[source, c]
----
AF_INET  IPv4 IP addresses and ports
AF_INET6 IPv6 IP addresses and ports and can be dual IPv4/IPv6 stacked
AF_UNIX  Unix Domain using file path names
----

which are all handled by the *coap_address_t* structure.

*Structure coap_address_t*

[source, c]
----
/* Multi-purpose address abstraction */
typedef struct coap_address_t {
  socklen_t size;           /* size of addr */
  union {
    struct sockaddr         sa;
    struct sockaddr_in      sin;
    struct sockaddr_in6     sin6;
    struct coap_sockaddr_un cun; /* CoAP shortened special */
  } addr;
} coap_address_t;

which is used in the *coap_addr_info_t* structure as returned by
*coap_resolve_address_info()*.
----

*Structure coap_addr_info_t*

[source, c]
----
/* Resolved addresses information */
typedef struct coap_addr_info_t {
  struct coap_addr_info_t *next; /* Next entry in the chain */
  coap_uri_scheme_t scheme;      /* CoAP scheme to use */
  coap_proto_t proto;            /* CoAP protocol to use */
  coap_address_t addr;           /* The address to connect / bind to */
} coap_addr_info_t;
----

*Structure coap_sockaddr_un*

[source, c]
----
#define COAP_UNIX_PATH_MAX   (sizeof(struct sockaddr_in6) - sizeof(sa_family_t))

struct coap_sockaddr_un {
        sa_family_t sun_family; /* AF_UNIX */
        char sun_path[COAP_UNIX_PATH_MAX];   /* pathname max 26 with NUL byte */
};
----

The *coap_sockaddr_un* structure is modeled on the *sockaddr_un* structure
(see *unix*(7)) but has a smaller sun_path so that the overall size of
*coap_address_t* is not changed by its inclusion. COAP_UNIX_MAX_PATH includes
the trailing zero terminator of a domain unix file name.

*Enum coap_resolve_type_t*

[source, c]
----
typedef enum coap_resolve_type_t {
  COAP_RESOLVE_TYPE_LOCAL,   /* local side of session */
  COAP_RESOLVE_TYPE_REMOTE,  /* remote side of session */
} coap_resolve_type_t;
----

Used when determining how to do an address lookup when calling
*coap_resolve_address_info()*.

FUNCTIONS
---------

*Function: coap_address_init()*

The *coap_address_init*() function initializes _addr_ for later use.  In
particular it sets the _size_ variable and sets all other values to be 0.

It is then the responsibility of the application to set the address family
in addr.sa.sa_family and then fill in the the appropriate union structure
based on the address family before the coap_address_t _addr_ is used.

*Function: coap_address_copy()*

The *coap_address_copy*() function copies the address _src_ into _dst_.

*Function: coap_address_equals()*

The *coap_address_equals*() function checks whether the addresses _a_ and _b_
are identical.

*Function: coap_address_get_port()*

The *coap_address_get_port*() function gets the the port from _addr_ if
_addr_ is AF_INET or AF_INET6.

*Function: coap_address_set_port()*

The *coap_address_set_port*() function sets the the _port_ in _addr_ if
_addr_ is AF_INET or AF_INET6.

*Function: coap_get_available_scheme_hint_bits()*

The *coap_get_available_scheme_hint_bits*() function is used for servers to
determine what coap schemes are supported in the underlying libcoap library.
_have_pki_psk_ can be set to 1 to check for (D)DTLS support, else 0.
_ws_check_ can be set to 1 to check for WebSockets support, else 0.
_use_unix_proto_, if not set to COAP_PROTO_NONE, hints at the specific CoAP
protocol to use over a Unix socket. The output is suitable for input for the
*coap_address_resolve_info*()'s _scheme_hint_bits_.

*Function: coap_resolve_address_info()*

The *coap_resolve_address_info*() function resolves the address _address_ into
a set of one or more coap_addr_info_t structures. Depending on the scheme as
abstracted from _scheme_hint_bits_,  _port_, _secure_port_, _ws_port_
(WebSockets) or _ws_secure_port_ (WebSockets) is used to
update the addr variable of coap_addr_info_t. If _port_ (or _secure_port_) is
0, then the default port for the scheme is used if _type_ is set to
COAP_RESOLVE_TYPE_LOCAL.  _ai_hints_flags_ is used for
the internally called getaddrinfo(3) function. _scheme_hint_bits_ is a set of
one or more COAP_URI_SCHEME_*_BIT or'd together. _scheme_hint_bits_ can also
(for servers) be the output from *coap_get_available_scheme_hint_bits*().

The returned set of coap_addr_info_t structures must be freed off by the
caller using *coap_free_address_info*().

*Function: coap_free_address_info()*

The *coap_free_address_info*() function frees off all the _info_list_
linked entries.

*Function: coap_address_set_unix_domain()*

The *coap_address_set_unix_domain*() function initializes _addr_ and then
populates _addr_ with all the appropriate information for a Unix Domain Socket.
The _host_ information with length _host_len_ abstracted from a CoAP URI
is copied into _addr_'s _sun_path_ translating any %2F encoding to /.

*Function: coap_host_is_unix_domain()*

The *coap_host_is_unix_domain*() function checks whether _host_ is an
an AF_UNIX file name (encoded using %2F to indicate a /).

*Function: coap_is_mcast()*

The *coap_is_mcast*() function checks whether _addr_ is a multicast
address.

*Function: coap_is_bcast()*

The *coap_is_mcast*() function checks whether _addr_ is a broadcast
address.

*Function: coap_is_af_unix()*

The *coap_is_mcast*() function checks whether _addr_ is of the type AF_UNIX.

RETURN VALUES
-------------
*coap_address_equals*() returns 1 if the addresses are equal or 0 if not.

*coap_address_get_port*() returns the port in network byte order.

*coap_get_available_scheme_hint_bits*() returns a set of COAP_URI_SCHEME_*_BIT
or'd together based on the supported libcoap functionality.

*coap_resolve_address_info*() returns a linked list of addresses that can be
used for session setup or NULL if there is a failure.

*coap_address_set_unix_domain*() returns 1 on success or 0 on failure.

*coap_host_is_unix_domain*() returns 1 if encoded unix path name or 0 if not.

*coap_is_mcast*() returns 1 if address is multicast or 0 if not.

*coap_is_bcast*() returns 1 if address is broadcast or 0 if not.

*coap_is_af_unix*() returns 1 if address is of type AF_UNIX or 0 if not.

EXAMPLES
--------
*Get client target address from uri*
[source, c]
----
#include <coap@LIBCOAP_API_VERSION@/coap.h>

static int
get_address(coap_uri_t *uri, coap_address_t *dst) {
  coap_addr_info_t *info_list;

  info_list = coap_resolve_address_info(&uri->host, uri->port, uri->port,
                                        uri->port, uri->port,0,
                                        1 << uri->scheme, COAP_RESOLVE_TYPE_LOCAL);
  if (info_list == NULL)
    return 0;
  memcpy(dst, &info_list->addr, sizeof(*dst));
  coap_free_address_info(info_list);
  return 1;
}
----

SEE ALSO
--------
*coap_endpoint_client*(3), *coap_endpoint_server*(3) and *coap_uri*(3)

FURTHER INFORMATION
-------------------
See

"https://rfc-editor.org/rfc/rfc7252[RFC7252: The Constrained Application Protocol (CoAP)]"

for further information.

BUGS
----
Please raise an issue on GitHub at
https://github.com/obgm/libcoap/issues to report any bugs.

Please raise a Pull Request at https://github.com/obgm/libcoap/pulls
for any fixes.

AUTHORS
-------
The libcoap project <<EMAIL>>
