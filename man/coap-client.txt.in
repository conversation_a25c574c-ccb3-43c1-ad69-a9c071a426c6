// -*- mode:doc; -*-
// vim: set syntax=asciidoc tw=0

coap-client(5)
==============
:doctype: manpage
:man source:   coap-client
:man version:  @PACKAGE_VERSION@
:man manual:   coap-client Manual

NAME
-----
coap-client,
coap-client-gnutls,
coap-client-mbedtls,
coap-client-openssl,
coap-client-notls
- CoAP Client based on libcoap

SYNOPSIS
--------
*coap-client* [*-a* addr] [*-b* [num,]size] [*-e* text] [*-f* file] [*-l* loss]
              [*-m* method] [*-o* file] [*-p* port] [*-q* tls_engine_conf_file]
              [*-r*] [*-s duration*]
              [*-t* type] [*-v* num] [*-w*] [*-A* type] [*-B* seconds]
              [*-E* oscore_conf_file[,seq_file]] [*-G* count] [*-H* hoplimit]
              [*-K* interval] [*-L* value] [*-N*] [*-O* num,text]
              [*-P* scheme://addr[:port]] [*-T* token] [*-U*] [*-V* num]
              [*-X* size]
              [*-d* value]
              [[*-h* match_hint_file] [*-k* key] [*-u* user] [*-2*]]
              [[*-c* certfile] [*-j* keyfile] [*-n*] [*-C* cafile]
              [*-J* pkcs11_pin] [*-M* rpk_file] [*-R* trust_casfile]] URI

For *coap-client* versions that use libcoap compiled for different
(D)TLS libraries, *coap-client-notls*, *coap-client-gnutls*,
*coap-client-openssl*, *coap-client-mbedtls* or *coap-client-tinydtls* may be
available.  Otherwise, *coap-client* uses the default libcoap (D)TLS support.

DESCRIPTION
-----------
*coap-client* is a CoAP client to communicate with 6LoWPAN devices via
the protocol CoAP (RFC 7252) using the URI given as argument on the
command line. The URI must have the scheme 'coap', 'coap+tcp', 'coaps' or
'coaps+tcp'.

'coaps' and 'coaps+tcp' are only supported when
coap-client is built with support for secure (D)TLS communication.

If 'coaps' or 'coaps+tcp' is being used, provided the CoAP server supports PKI
and is configured with a certificate and private key, the coap-client does not
need to have a Pre-Shared Key (-k) or certificate (-c) configured.

The URI's host part may be a DNS name, a literal IP address or a Unix domain
name. For Unix domain names, %2F is used as the / separator to differentiate
between the host and patch definitions. For IPv6 address references, angle
brackets are required (c.f. EXAMPLES) to delimit the host portion of the URI.

OPTIONS - General
-----------------
*-a* addr::
   The local address of the interface that has to be used. +
   Note: Do not use this option if the interface is likely to be transient -
   i.e. it is a tunnel interface that may come and go, as this is likely to
   cause "No such device" errors on transmission.

*-b* [num,]size::
   The block size to be used in GET/PUT/POST requests (value must be 16, 32,
   64, 128, 256, 512 or 1024). If 'num' is present, the request
   chain will start at block 'num'. When the server includes a Block2
   option in its response to a GET request, coap-client will automatically
   retrieve the subsequent block from the server until there are no more
   outstanding blocks for the requested content.

*-e* text::
   Include text as payload (use percent-encoding for non-ASCII characters).

*-f* file::
   File to send with PUT/POST (use '-' for STDIN).

*-l* list::
   Fail to send some datagrams specified by a comma separated list of
   numbers or number ranges (debugging only).

*-l* loss%::
   Randomly failed to send datagrams with the specified probability - 100%
   all datagrams, 0% no datagrams (debugging only).

*-m* method::
   The request method for action (get|put|post|delete), default is 'get'.
   (Note that the string passed to *-m* is compared case-insensitive.)

*-o* file::
   A filename to store data retrieved with GET.

*-p* port::
   The port to send from.

*-q* tls_engine_conf_file::
   'tls_engine_conf_file' contains TLS ENGINE configuration. Only OpenSSL
   is currently supported.
   See *coap-tls-engine-conf*(5) for definitions.

*-r*::
   Use reliable protocol (TCP or TLS).

*-s* duration::
   Subscribe to / observe the resource specified by URI for the given
   'duration' in seconds.

*-t* type::
   Content format for given resource for PUT/POST. 'type' must be either
   a numeric value reflecting a valid CoAP content format or a string
   describing a registered format. The following registered content format
   descriptors are supported, with alternative shortcuts given in
   parentheses:

     text/plain (plain)
     application/link-format (link, link-format)
     application/xml (xml)
     application/octet-stream (binary, octet-stream)
     application/exi (exi)
     application/json (json)
     application/cbor (cbor)

*-v* num::
   The verbosity level to use (default 4, maximum is 8) for general
   CoAP logging.

*-w*::
   Append a newline to received data.

*-A* type::
   Accepted media type. 'type' must be either a numeric value reflecting a
   valid CoAP content format or a string that specifies a registered format as
   described for option *-t*.

*-B* seconds::
   Break operation after waiting given seconds (default is 90).

*-E* oscore_conf_file[,seq_file]::
   'oscore_conf_file' contains OSCORE configuration. See *coap-oscore-conf*(5)
   for definitions. Optional 'seq_file' (which is created if needed) is used to
   save the current transmit sequence number, so on client restart sequence
   numbers continue to increase and are not reset to prevent anti-replay
   mechanisms being triggered.

*-G* count ::
   Repeat the Request 'count' times with a second delay between each one.
   Must have a value between 1 and 255 inclusive. Default is '1'.

*-H* hoplimit::
   Set the Hop Limit count to hoplimit for proxies. Must have a value between
   1 and 255 inclusive. Default is '16'.

*-K* interval::
   Send a ping after interval seconds of inactivity.
   If not specified (or 0), keep-alive is disabled (default).

*-L* value::
   Sum of one or more COAP_BLOCK_* flag values for different block handling
   methods. Default is 1 (COAP_BLOCK_USE_LIBCOAP).

     COAP_BLOCK_USE_LIBCOAP         1
     COAP_BLOCK_SINGLE_BODY         2
     COAP_BLOCK_TRY_Q_BLOCK         4
     COAP_BLOCK_USE_M_Q_BLOCK       8
     COAP_BLOCK_NO_PREEMPTIVE_RTAG 16
     COAP_BLOCK_STLESS_FETCH       32

*-N* ::
   Send NON-confirmable message. If option *-N* is not specified, a
   confirmable message will be sent.

*-O* num,text::
   Add option 'num' with contents of 'text' to the request. If the text begins
   with 0x, then the hex text (two [0-9a-f] per byte) is converted to binary
   data.

*-P* scheme://addr[:port]::
   Scheme, address and optional port to define how to connect to a CoAP proxy
   (automatically adds Proxy-Uri option to request) to forward the request to.
   Scheme is one of coap, coaps, coap+tcp, coaps+tcp, coap+ws, and coaps+ws.

*-T* token::
   Define the initial starting 'token' for the request (up to 24 characters).

*-U* ::
   Never include Uri-Host or Uri-Port options.

*-V* num::
   The verbosity level to use (default 3, maximum is 7) for (D)TLS
   library logging.

*-X* size::
   Maximum message size to use for TCP based connections (default is 8388864).
   Maximum value of 2^32 -1.

OPTIONS - DTLS
--------------
(If supported by underlying (D)TLS library)

*-d* count::
   For DTLS, enable use of Connection-ID (RFC9176). If the count is not 0,
   then the client will changes its source port every count packets to test
   CID.

OPTIONS - PSK
-------------
(If supported by underlying (D)TLS library)

*-h* match_hint_file::
   This is a file that contains one or more lines of received Identity Hints
   to match to use different user identity and associated pre-shared key (PSK)
   (comma separated) instead of the *-k key* and *-u user* options. E.g., per
   line +
   hint_to_match,use_user,with_key +
   A line that starts with # is treated as a comment. +
   Note: *-k key* and *-u user* still need to be defined for the default case in
   case there is no match.

*-k* key::
   Pre-shared key for the specified user identity (*-u* option also required). +
   If the key begins with 0x, then the hex text (two [0-9a-f] per byte) is
   converted to binary data.

*-u* user::
   User identity to send for pre-shared key mode (*-k* option also required).

*-2* ::
   Use EC-JPAKE negotiation (if supported).

OPTIONS - PKI
-------------
(If supported by underlying (D)TLS library)

*Note:* If any one of *certfile*, *keyfile* or *cafile* is in PKCS11 URI
naming format (pkcs11: prefix), then any remaining non PKCS11 URI file
definitions have to be in DER, not PEM, format.  Otherwise all of
*certfile*, *keyfile* or *cafile* are in PEM format.

*-c* certfile::
   PEM file or PKCS11 URI for the certificate. The private key can also be in
   the PEM file, or has the same PKCS11 URI. If not, the private key is defined
   by *-j keyfile*.

*-j* keyfile::
   PEM file or PKCS11 URI for the private key for the certificate in *-c
   certfile* if the parameter is different from certfile in *-c certfile*.

*-n* ::
  Disable remote peer certificate checking.

*-C* cafile::
PEM file or PKCS11 URI for the CA certificate and any intermediate CAs that was
  used to sign the server
  certfile. Ideally the client certificate should be signed by the same CA so
  that mutual authentication can take place. The contents of cafile are added
  to the trusted store of root CAs. Using the *-C* or *-R* options will trigger
  the validation of the server certificate unless overridden by the *-n* option.

*-J* pkcs11_pin::
  The user pin to unlock access to the PKCS11 token.

*-M* rpk_file::
  Raw Public Key (RPK) PEM file or PKCS11 URI that contains both PUBLIC KEY
  and PRIVATE KEY or just EC PRIVATE KEY. (GnuTLS and TinyDTLS(PEM) support
  only).  *-C cafile* or *-R trust_casfile* are not required.

*-R* trust_casfile::
  PEM file containing the set of trusted root CAs that are to be used to
  validate the server certificate. Alternatively, this can point to a
  directory containing a set of CA PEM files. The *-C cafile* CA does not have
  to be in this list and is trusted for the validation. Using
  *-R trust_casfile* disables common CA mutual authentication which can only
  be done by using *-C cafile*. Using the *-C* or *-R* options will
  trigger the validation of the server certificate unless overridden by the
  *-n* option.

EXAMPLES
--------
* Example
----
coap-client coap://libcoap.net
----
Query the resource '/' from server 'libcoap.net' (using the GET method).

* Example
----
coap-client -m get coap://[::1]/
----
Query the resource '/' on localhost using the 'GET' method to get back the
summary information.

* Example
----
coap-client -m get -P coap://upstream-proxy coap://[::1]/
----
Query the resource '/' on 'upstream-proxy' using the 'GET' method to get back
the summary information.

* Example
----
coap-client -m get coap://%2Fsome%2Funix%2Fdomain%2Fpath/time
----
Query the resource '/time' on server listening on datagram Unix domain
'/some/unix/domain/path' using the 'GET' method to get back the
current time. The %2F is the hex encoding for / and indicates
which is the 'host' definition separator and the simple / is for the path
definition separator.

* Example
----
coap-client -m get coap://[::1]/.well-known/core
----
Query on the resource '.well-known/core' on localhost to get back a list of
the known resources along with their attribute definitions.

* Example
----
echo -n "mode=on" | coap-client -m put \
coap://[2001:db8:c001:f00d:221:2eff:ff00:2704]:5683/actuators/leds?color=r -f-
----
Send text 'mode=on' to resource 'actuators/leds?color=r' on the endpoint with
address '2001:db8:c001:f00d:221:2eff:ff00:2704' and port '5683'. Note that the
port '5683' is the default port and isn't actually required in this instance.

* Example
----
coap-client -m put coap://[fec0::3]/ck -T 3a -t binary -f to_upload
----
Put the contents of file 'to_upload' with content type 'binary' (i.e.
application/octet-stream) into resource 'ck' on 'fec0::3' using a token of
'3a' via the 'PUT' method.

FILES
------
There are no configuration files.

EXIT STATUS
-----------
*0*::
   Success

*1*::
   Failure (syntax or usage error; configuration error; document
   processing failure; unexpected error)

SEE ALSO
--------

*coap-server*(5) and *coap-oscore-conf*(5)

BUGS
-----
Please raise an issue on GitHub at
https://github.com/obgm/libcoap/issues to report any bugs.

Please raise a Pull Request at https://github.com/obgm/libcoap/pulls
for any fixes.

AUTHORS
-------
The libcoap project <<EMAIL>>
