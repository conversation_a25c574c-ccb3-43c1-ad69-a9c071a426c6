// -*- mode:doc; -*-
// vim: set syntax=asciidoc tw=0

coap(7)
=======
:doctype: manpage
:man source:   coap
:man version:  @PACKAGE_VERSION@
:man manual:   libcoap Manual

NAME
----
coap - Overview of the libcoap library

SYNOPSIS
--------
Summary of the different libcoap API manual pages.

DESCRIPTION
-----------

libcoap is a C implementation of a lightweight application-protocol for
devices that are constrained by their resources such as computing power, RF
range, memory, bandwidth, or network packet sizes. This protocol, CoAP, is
standardized by the IETF as https://rfc-editor.org/rfc/rfc7252[RFC7252].
For further information related to Co<PERSON>, see http://coap.technology.

Documentation for the specific library calls with examples can be found in the
man pages referred to in SEE ALSO.

Further information can be found in the include header files with example
code provided in the examples directory.

*NOTE:* This documentation is a work in progress.  Any missing information can
be found in the include header files along with example code provided in the
examples directory.


SEE ALSO
--------
*coap_address*(3), *coap_async*(3), *coap_attribute*(3), *coap_block*(3),
*coap_cache*(3), *coap_context*(3), *coap_deprecated*(3), *coap_encryption*(3),
*coap_endpoint_client*(3), *coap_endpoint_server*(3), *coap_handler*(3),
*coap_init*(), *coap_io*(3), *coap_keepalive*(3), *coap_locking*(3),
*coap_logging*(3), *coap_lwip*(3),
*coap_observe*(3), *coap_oscore*(3), *coap_pdu_access*(3), *coap_pdu_setup*(3),
*coap_persist*(3), *coap_recovery*(3), *coap_resource*(3), *coap_session*(3),
*coap_string*(3), *coap_tls_library*(3), *coap_uri*(3) and *coap_websockets*(3)

For example executables, see *coap-client*(5), *coap-rd*(5) and *coap-server*(5)

For OSCORE configuration, see  *coap-oscore-conf*(5)

FURTHER INFORMATION
-------------------
See

"https://rfc-editor.org/rfc/rfc7252[RFC7252: The Constrained Application Protocol (CoAP)]"

"https://rfc-editor.org/rfc/rfc7390[RFC7390: Group Communication for the Constrained Application Protocol (CoAP)]"

"https://rfc-editor.org/rfc/rfc7641[RFC7641: Observing Resources in the Constrained Application Protocol (CoAP)]"

"https://rfc-editor.org/rfc/rfc7959[RFC7959: Block-Wise Transfers in the Constrained Application Protocol (CoAP)]"

"https://rfc-editor.org/rfc/rfc7967[RFC7967: Constrained Application Protocol (CoAP) Option for No Server Response]"

"https://rfc-editor.org/rfc/rfc8132[RFC8132: PATCH and FETCH Methods for the Constrained Application Protocol (CoAP)]"

"https://rfc-editor.org/rfc/rfc8323[RFC8323: CoAP (Constrained Application Protocol) over TCP, TLS, and WebSockets]"

"https://rfc-editor.org/rfc/rfc8516[RFC8516: "Too Many Requests" Response Code for the Constrained Application Protocol]"

"https://rfc-editor.org/rfc/rfc8613[RFC8613: Object Security for Constrained RESTful Environments (OSCORE)]"

"https://rfc-editor.org/rfc/rfc8768[RFC8768: Constrained Application Protocol (CoAP) Hop-Limit Option]"

"https://rfc-editor.org/rfc/rfc8974[RFC8974: Extended Tokens and Stateless Clients in the Constrained Application Protocol (CoAP)]"

"https://rfc-editor.org/rfc/rfc9175[RFC9175: CoAP: Echo, Request-Tag, and Token Processing]"

"https://rfc-editor.org/rfc/rfc9177[RFC9177: Constrained Application Protocol (CoAP) Block-Wise Transfer Options Supporting Robust Transmission]"

for further information.

BUGS
----
Please raise an issue on GitHub at
https://github.com/obgm/libcoap/issues to report any bugs.

Please raise a Pull Request at https://github.com/obgm/libcoap/pulls
for any fixes.

AUTHORS
-------
The libcoap project <<EMAIL>>
