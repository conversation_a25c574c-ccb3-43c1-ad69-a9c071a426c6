// -*- mode:doc; -*-
// vim: set syntax=asciidoc tw=0

coap_logging(3)
===============
:doctype: manpage
:man source:   coap_logging
:man version:  @PACKAGE_VERSION@
:man manual:   libcoap Manual

NAME
----
coap_logging,
coap_log,
coap_log_emerg,
coap_log_alert,
coap_log_crit,
coap_log_err,
coap_log_warn,
coap_log_info,
coap_log_notice,
coap_log_debug,
coap_log_oscore,
coap_get_log_level,
coap_set_log_level,
coap_dtls_log,
coap_dtls_get_log_level,
coap_dtls_set_log_level,
coap_set_log_handler,
coap_log_level_desc,
coap_package_name,
coap_package_version,
coap_package_build,
coap_set_show_pdu_output,
coap_show_pdu,
coap_endpoint_str,
coap_session_str,
coap_print_addr,
coap_print_ip_addr
- Work with Co<PERSON> logging

SYNOPSIS
--------
*#include <coap@LIBCOAP_API_VERSION@/coap.h>*

*void coap_log(coap_log_t _level_, const char *_format_, _..._);*

*void coap_log_emerg(const char *_format_, _..._);*

*void coap_log_alert(const char *_format_, _..._);*

*void coap_log_crit(const char *_format_, _..._);*

*void coap_log_err(const char *_format_, _..._);*

*void coap_log_warn(const char *_format_, _..._);*

*void coap_log_info(const char *_format_, _..._);*

*void coap_log_notice(const char *_format_, _..._);*

*void coap_log_debug(const char *_format_, _..._);*

*void coap_log_oscore(const char *_format_, _..._);*

*void coap_set_log_level(coap_log_t _level_);*

*coap_log_t coap_get_log_level(void);*

*void coap_dtls_log(coap_log_t _level_, const char *_format_, _..._);*

*void coap_dtls_set_log_level(coap_log_t _level_);*

*coap_log_t coap_dtls_get_log_level(void);*

*void coap_set_log_handler(coap_log_handler_t _handler_);*

*const char *coap_log_level_desc(coap_log_t _level_);*

*const char *coap_package_name(void);*

*const char *coap_package_version(void);*

*const char *coap_package_build(void);*

*void coap_set_show_pdu_output(int _use_fprintf_);*

*void coap_show_pdu(coap_log_t _level_, const coap_pdu_t *_pdu_);*

*const char *coap_endpoint_str(const coap_endpoint_t *_endpoint_);*

*const char *coap_session_str(const coap_session_t *_session_);*

*size_t coap_print_addr(const coap_address_t *_address_,
unsigned char *_buffer_, size_t _length_);*

*const char *coap_print_ip_addr(const coap_address_t *_address_,
char *_buffer_, size_t _length_);*

For specific (D)TLS library support, link with
*-lcoap-@LIBCOAP_API_VERSION@-notls*, *-lcoap-@LIBCOAP_API_VERSION@-gnutls*,
*-lcoap-@LIBCOAP_API_VERSION@-openssl*, *-lcoap-@LIBCOAP_API_VERSION@-mbedtls*,
*-lcoap-@LIBCOAP_API_VERSION@-wolfssl*
or *-lcoap-@LIBCOAP_API_VERSION@-tinydtls*.   Otherwise, link with
*-lcoap-@LIBCOAP_API_VERSION@* to get the default (D)TLS library support.

DESCRIPTION
-----------
The logging sub-system supports logging at different levels, and depending on
the selected logging level, outputs the appropriate information.

Logging by default is to stderr or stdout depending on the logging level of
the log entry.  It is possible to send the logging information to an
application logging callback handler for processing by the application.

Logging levels (*coap_log_t*) are defined as follows (based on the *syslog*()
names and values):

*COAP_LOG_EMERG*::
Emergency level (0).

*COAP_LOG_ALERT*::
Alert level (1).

*COAP_LOG_CRIT*::
Critical level (2).

*COAP_LOG_ERR*::
Error level (3).

*COAP_LOG_WARN*::
Warning level (the default) (4).

*COAP_LOG_NOTICE*::
Notice level (5).

*COAP_LOG_INFO*::
Information level (6).

*COAP_LOG_DEBUG*::
Debug level (7).

With additional level:

*COAP_LOG_OSCORE*::
Debug OSCORE information (8).

*NOTE:* The maximum logging level in the libcoap library may have been updated
by the use of './configure --enable-max-logging-level=X' (where X is 0 to 8 inclusive)
which may disable some of the higher logging levels to save code space.

FUNCTIONS
---------

*Function: coap_log()*

The *coap_log*() function is used to log information at the appropriate _level_.
The rest of the parameters follow the standard *printf*() function format.
Where possible, the coap_log_*() functions should be used instead.

*Function: coap_log_emerg()*

The *coap_log_emerg*() function provides a wrapper to the *coap_log*() function
with _level_ set to COAP_LOG_EMERG.
The parameters follow the standard *printf*() function format.

*Function: coap_log_alert()*

The *coap_log_alert*() function provides a wrapper to the *coap_log*() function
with _level_ set to COAP_LOG_ALERT.
The parameters follow the standard *printf*() function format.

*Function: coap_log_crit()*

The *coap_log_crit*() function provides a wrapper to the *coap_log*() function
with _level_ set to COAP_LOG_CRIT.
The parameters follow the standard *printf*() function format.

*Function: coap_log_err()*

The *coap_log_err*() function provides a wrapper to the *coap_log*() function
with _level_ set to COAP_LOG_ERR.
The parameters follow the standard *printf*() function format.

*Function: coap_log_warn()*

The *coap_log_warn*() function provides a wrapper to the *coap_log*() function
with _level_ set to COAP_LOG_WARN.
The parameters follow the standard *printf*() function format.

*Function: coap_log_notice()*

The *coap_log_notice*() function provides a wrapper to the *coap_log*() function
with _level_ set to COAP_LOG_NOTICE.
The parameters follow the standard *printf*() function format.

*Function: coap_log_info()*

The *coap_log_info*() function provides a wrapper to the *coap_log*() function
with _level_ set to COAP_LOG_INFO.
The parameters follow the standard *printf*() function format.

*Function: coap_log_debug()*

The *coap_log_debug*() function provides a wrapper to the *coap_log*() function
with _level_ set to COAP_LOG_DEBUG.
The parameters follow the standard *printf*() function format.

*Function: coap_log_oscore()*

The *coap_log_oscore*() function provides a wrapper to the *coap_log*() function
with _level_ set to COAP_LOG_OSCORE.
The parameters follow the standard *printf*() function format.

*Function: coap_set_log_level()*

The *coap_set_log_level*() function is used to set the current logging _level_
for output by any subsequent *coap_log*() calls.  Output is only logged if the
*coap_log*() _level_ definition is smaller than or equal to the current logging
_level_.

*Function: coap_get_log_level()*

The *coap_get_log_level*() function is used to get the current logging level.

*Function: coap_dtls_log()*

The *coap_dtls_log*() function is used to log (D)TLS library information at the
appropriate _level_.  The rest of the parameters follow the standard *printf*()
function format.

*Function: coap_dtls_set_log_level()*

The *coap_dtls_set_log_level*() function is used to set the current logging
_level_ for output by any subsequent *coap_dtls_log*() calls.  Output is only
logged if the *coap_dtls_log*() _level_ definition is smaller than or equal to
the current DTLS logging _level_.

*Function: coap_dtls_get_log_level()*

The *coap_dtls_get_log_level*() function is used to get the current logging
level for the DTLS library.

*Function: coap_set_log_handler()*

The *coap_set_log_handler*()* function can be used to define an alternative
logging handler for processing any logging messages.  The logging handler
prototype is defined as:

[source, c]
----
typedef void (*coap_log_handler_t) (coap_log_t level, const char *message);
----

*Function: coap_log_level_desc()*

The *coap_log_level_desc*()* function can be used to return a 4 character ascii
description for the logging _level_.

*Function: coap_package_name()*

The *coap_package_name*() function returns the name of this library.

*Function: coap_package_version()*

The *coap_package_version*() function returns the version of this library.

*Function: coap_package_build()*

The *coap_package_build*() function returns the git information (as in
"git describe --tags") for the build of this library or version of this
library if git is not used.

*Function: coap_set_show_pdu_output()*

The *coap_set_show_pdu_output*() function defines whether the output from
*coap_show_pdu*() is to be either sent to stdout/stderr, or output using
*coap_log*().  _use_fprintf_ is set to 1 for stdout/stderr (the default), and
_use_fprintf_ is set to 0 for *coap_log*().

*Function: coap_show_pdu()*

The *coap_show_pdu*() function is used to decode the _pdu_, outputting as
appropriate for logging _level_.  Where the output goes is dependent on
*coap_set_show_pdu_output*().

*NOTE:* If _pdu_ has not been associated with a CoAP session (i.e. not a
received PDU or coap_send() not yet called), then the output assumes that
this _pdu_ is of type unreliable.

*Function: coap_endpoint_str()*

The *coap_endpoint_str*() function returns a description string of the
_endpoint_.

*Function: coap_session_str()*

The *coap_session_str*() function is used to get a string containing the
information about the _session_.

*Function: coap_print_addr()*

The *coap_print_addr*() function returns the length of the  description string
containing the IP address and port from _address_, updating _buffer_ which has a
maximum length _length_.

*Function: coap_print_ip_addr()*

The *coap_print_ip_addr*() function returns a description string of the
IP address only for _address_. _buffer_ is updated, which has a maximum length of
_length_.

RETURN VALUES
-------------

*coap_package_name*(), *coap_package_version*() and
*coap_package_build*() return the appropriate zero-terminated character
string.

*coap_get_log_level*() returns the current logging level.

*coap_dtls_get_log_level*() returns the current logging level
for the DTLS library specifics.

*coap_log_level_desc*() returns a description string of the logging
_level_.

*coap_endpoint_str*() returns a description string of the
_endpoint_.

*coap_session_str*() returns a description string of the
_session_.

*coap_print_addr*() returns the length of _buffer_ that has been
updated with an ascii readable IP address and port for _address_.

*coap_print_ip_addr*() returns a pointer to the ascii readable
IP address only for _address_.

SEE ALSO
--------
*coap_context*(3) and *coap_session*(3)

FURTHER INFORMATION
-------------------
See

"https://rfc-editor.org/rfc/rfc7252[RFC7252: The Constrained Application Protocol (CoAP)]"

for further information.

BUGS
----
Please raise an issue on GitHub at
https://github.com/obgm/libcoap/issues to report any bugs.

Please raise a Pull Request at https://github.com/obgm/libcoap/pulls
for any fixes.

AUTHORS
-------
The libcoap project <<EMAIL>>
