// -*- mode:doc; -*-
// vim: set syntax=asciidoc tw=0

coap_websockets(3)
==================
:doctype: manpage
:man source:   coap_websockets
:man version:  @PACKAGE_VERSION@
:man manual:   libcoap Manual

NAME
----
coap_websockets,
coap_ws_set_host_request
- Work with CoAP WebSockets

SYNOPSIS
--------
*#include <coap@LIBCOAP_API_VERSION@/coap.h>*

*int coap_ws_set_host_request(coap_session_t *_session_,
coap_str_const_t *_ws_host_);*

DESCRIPTION
-----------
This man page describes the additional libcoap functions that are available
for working with the optional WebSockets support.

CoAP Servers can be set up to listen for incoming WebSockets requests, and
CoAP Clients can initiate WebSockets requests using the CoAP schemes coap_ws://
and coaps+ws://.

FUNCTIONS
---------

*Function: coap_ws_set_host_request()*

The *coap_ws_set_host_request*() function is used to set _ws_host_ as the HTTP
Host: for the _session_ in a WebSockets request.

RETURN VALUES
-------------
*coap_ws_set_host_request*() returns 1 if successful, else 0 if a failure.

SEE ALSO
--------
*coap_supported*(3) and *coap_uri*(3)

FURTHER INFORMATION
-------------------
See

"https://rfc-editor.org/rfc/rfc7252[RFC7252: The Constrained Application Protocol (CoAP)]"

"https://rfc-editor.org/rfc/rfc8323[RFC8323: CoAP (Constrained Application Protocol) over TCP, TLS, and WebSockets]"

for further information.

BUGS
----
Please raise an issue on GitHub at
https://github.com/obgm/libcoap/issues to report any bugs.

Please raise a Pull Request at https://github.com/obgm/libcoap/pulls
for any fixes.

AUTHORS
-------
The libcoap project <<EMAIL>>
