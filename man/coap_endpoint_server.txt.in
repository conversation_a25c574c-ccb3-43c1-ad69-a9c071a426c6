// -*- mode:doc; -*-
// vim: set syntax=asciidoc tw=0

coap_endpoint_server(3)
=======================
:doctype: manpage
:man source:   coap_endpoint_server
:man version:  @PACKAGE_VERSION@
:man manual:   libcoap Manual

NAME
----
coap_endpoint_server,
coap_context_set_pki,
coap_context_set_pki_root_cas,
coap_context_set_psk2,
coap_new_endpoint,
coap_free_endpoint,
coap_endpoint_set_default_mtu,
coap_join_mcast_group_intf,
coap_mcast_per_resource
- Work with CoAP server endpoints

SYNOPSIS
--------
*#include <coap@LIBCOAP_API_VERSION@/coap.h>*

*int coap_context_set_pki(coap_context_t *_context_,
const coap_dtls_pki_t *_setup_data_);*

*int coap_context_set_pki_root_cas(coap_context_t *_context_,
const char *_ca_file_, const char *_ca_dir_);*

*int coap_context_set_psk2(coap_context_t *_context_,
coap_dtls_spsk_t *setup_data);*

*coap_endpoint_t *coap_new_endpoint(coap_context_t *_context_,
const coap_address_t *_listen_addr_, coap_proto_t _proto_);*

*void coap_free_endpoint(coap_endpoint_t *_endpoint_);*

*void coap_endpoint_set_default_mtu(coap_endpoint_t *_endpoint_,
unsigned _mtu_);*

*int coap_join_mcast_group_intf(coap_context_t *_context_,
const char *_groupname_, const char *_ifname_);*

*void coap_mcast_per_resource(coap_context_t *_context_);*

For specific (D)TLS library support, link with
*-lcoap-@LIBCOAP_API_VERSION@-notls*, *-lcoap-@LIBCOAP_API_VERSION@-gnutls*,
*-lcoap-@LIBCOAP_API_VERSION@-openssl*, *-lcoap-@LIBCOAP_API_VERSION@-mbedtls*,
*-lcoap-@LIBCOAP_API_VERSION@-wolfssl*
or *-lcoap-@LIBCOAP_API_VERSION@-tinydtls*.   Otherwise, link with
*-lcoap-@LIBCOAP_API_VERSION@* to get the default (D)TLS library support.

DESCRIPTION
-----------
This man page focuses on the setting up of a CoAP server endpoint. For a CoAP
client endpoint, see *coap_endpoint_client*(3).

The CoAP stack's global state is stored in a coap_context_t _context_ object.
Resources, Endpoints and Sessions are associated with this _context_ object.
There can be more than one coap_context_t object per application, it is up to
the application to manage each one accordingly.

A CoAP _session_ maintains the state of an ongoing connection between a Client
and Server which is stored in a coap_session_t _session_ object. A CoAP
_session_ is tracked by local port, CoAP protocol, remote IP address and
remote port, or in the case of Unix Domain sockets, the local path and the
remote path.

The _session_ network traffic can be encrypted or un-encrypted if there is an
underlying TLS library.

If (D)TLS is going to be used for encrypting the network traffic, then the
(D)TLS
information for Pre-Shared Keys (PSK) or Public Key Infrastructure (PKI) needs
to be configured before any network traffic starts to flow. For Servers, this
has to be done before the Endpoint is created.

For Servers, all the encryption information is held internally by the (D)TLS
context level and the CoAP _context_ level as the Server is listening for new
incoming traffic based on the Endpoint definition.  The (D)TLS and CoAP
_session_ will not get built until the new traffic starts, which is done by the
libcoap library.

In principle the set-up sequence for CoAP Servers looks like
----
coap_new_context()
coap_context_set_pki_root_cas() - if the root CAs need to be updated and using PKI
coap_context_set_pki() and/or coap_context_set_psk2() - if encryption is required
coap_new_endpoint()
----

Multiple endpoints can be set up per _context_, each listening for a new traffic
flow with different TCP/UDP protocols, (D)TLS protocols, port numbers, Unix
pathnames etc. When
a new traffic flow is started, then the CoAP library will create and start a new
server _session_.

FUNCTIONS
---------

*Function: coap_context_set_pki()*

The *coap_context_set_pki*() function, for a specific _context_, is used to
configure the (D)TLS context using the _setup_data_ PKI variables as defined in
the coap_dtls_pki_t structure  - see *coap_encryption*(3). This should not be
used for setting up client sessions.

*Function: coap_context_set_pki_root_cas()*

The *coap_context_set_pki_root_cas*() function is used to define a set of
root CAs to be used instead of the default set of root CAs provided as a part
of the TLS library.  _ca_file_ points to a PEM encoded file containing the
list of CAs.  _ca_file_ can be NULL.  _ca_dir_ points to a directory
containing a set of PEM encoded files containing rootCAs.  _ca_dir_ can be
NULL. One or both of _ca_file_ and _ca_dir_ must be set. +
*NOTE:* Some TLS libraries send the full list of CAs added by this function
during the (D)TLS session setup handshakes. To stop this, either provide a
single CA using the _ca_file_ definition in _pki_key_ in _setup_data_ variable
when calling *coap_context_set_pki*(), or set _check_common_ca_ to 0 in
_setup_data_ variable. See *coap_encryption*(3).

*Function: coap_context_set_psk2()*

The *coap_context_set_psk2*() function is used to configure the (D)TLS context
using the _setup_data_ PSK variables as defined in the
coap_dtls_spsk_t structure  - see *coap_encryption*(3).
This function can only be used for servers as _setup_data_ provides
a _hint_, not an _identity_.

*Function: coap_new_endpoint()*

The *coap_new_endpoint*() function creates a new endpoint for _context_ that
is listening for new traffic as defined in _listen_addr_
(see *coap_address_t*(3)). If the address family is AF_INET or AF_INET6, then it
listens on the IP address and port number defined by _listen_addr_. If the
port number is 0, then the default CoAP port is used. If the address family is
AF_UNIX, then it listens on the defined unix domain path which has to be
unique per endpoint. This unique unix domain path will get deleted on
clean application exit.

Different CoAP protocols can be defined for _proto_ - the current supported
list is:

[source, c]
----
COAP_PROTO_UDP
COAP_PROTO_DTLS
COAP_PROTO_TCP
COAP_PROTO_TLS
COAP_PROTO_WS
COAP_PROTO_WSS
----

*coap_tcp_is_supported*(3), *coap_dtls_is_supported*(3),
*coap_tls_is_supported*(3), *coap_ws_is_supported*(3) and
*coap_wss_is_supported*(3) can be used for checking whether the underlying
TCP, (D)TLS or WebSocket protocol support is available.
See *coap_tls_library(3)* for further information on the types of (D)TLS
sessions supported.

When traffic starts to come in from a client, a server CoAP session is created
associated with this endpoint. This CoAP session is created with a reference
count of 0. This means that if the server session is not used for 5 minutes,
then it will get completely freed off.  See *coap_session_reference*(3) and
*coap_session_release*(3) for further information.

*Function: coap_free_endpoint()*

The *coap_free_endpoint*() function must be used to free off the _endpoint_.
It clears out all the sessions associated with this endpoint along with
any data associated with the sessions as well as
deleting the unix domain path if the address family is AF_UNIX.

*Function: coap_endpoint_set_default_mtu()*

The *coap_endpoint_set_default_mtu*() function is used to set the MTU size
(the maximum message size) of the data in a packet, excluding any IP or
TCP/UDP overhead to _mtu_ for the _endpoint_.  A sensible default is 1280.

*Function: coap_join_mcast_group_intf()*

The *coap_join_mcast_group_intf*() function is used to join the currently
defined endpoints that are UDP, associated with _context_, to the defined
multicast group _groupname_.  If _ifname_ is not NULL, then the multicast group
is associated with this interface, otherwise the underlying O/S will choose the
first appropriate interface. When the endpoint is freed off, the associated
multicast group will be removed. The registered multicast addresses for CoAP
are ***********, ff0x::fd (Variable-Scope) - i.e. ff02::fd (Link-Local) and
ff05::fd (Site-Local).

*NOTE:* multicast is not supported for address family type AF_UNIX.

*Function: coap_mcast_per_resource()*

The *coap_mcast_per_resource*() function enables mcast to be controlled on a
per resource basis giving the server application flexibility in how to respond
to mcast requests. With this enabled, this is done through additional flag
definitions when setting up each resource. See *coap_resource*(3).

RETURN VALUES
-------------
*coap_context_set_pki*(), *coap_context_set_pki_root_cas*() and
*coap_context_set_psk2*() return 1 on success, 0 on failure.

*coap_new_endpoint*() returns a newly created endpoint or
NULL if there is a creation failure.

*coap_join_mcast_group_intf*() returns 0 on success, -1 on failure.

EXAMPLES
--------
*CoAP Server Non-Encrypted Setup*

[source, c]
----
#include <coap@LIBCOAP_API_VERSION@/coap.h>

static coap_context_t *
setup_server_context(void) {
  coap_endpoint_t *endpoint;
  coap_address_t listen_addr;
  coap_context_t *context = coap_new_context(NULL);

  if (!context)
    return NULL;
  /* See coap_block(3) */
  coap_context_set_block_mode(context,
                              COAP_BLOCK_USE_LIBCOAP | COAP_BLOCK_SINGLE_BODY);


  /* See coap_address(3) */
  coap_address_init(&listen_addr);
  listen_addr.addr.sa.sa_family = AF_INET;
  listen_addr.addr.sin.sin_port = htons(5683);

  endpoint = coap_new_endpoint(context, &listen_addr, COAP_PROTO_UDP);
  if (!endpoint) {
    coap_free_context(context);
    return NULL;
  }

  /* Initialize resources - See coap_resource(3) init_resources() example */

  return context;
}
----

*CoAP Server Non-Encrypted Unix Domain Setup*

[source, c]
----
#include <coap@LIBCOAP_API_VERSION@/coap.h>

#include <stdio.h>
#include <unistd.h>

/* This need to be unique per endpoint */
#define UNIX_DOMAIN_LISTEN_DGRAM "/tmp/server.dgram"

static coap_context_t *
setup_server_context(void) {
  coap_endpoint_t *endpoint;
  coap_address_t listen_addr;
  coap_context_t *context = coap_new_context(NULL);

  if (!context)
    return NULL;
  /* See coap_block(3) */
  coap_context_set_block_mode(context,
                              COAP_BLOCK_USE_LIBCOAP | COAP_BLOCK_SINGLE_BODY);


  /* See coap_address(3) */
  coap_address_set_unix_domain(&listen_addr,
                               (const uint8_t *)UNIX_DOMAIN_LISTEN_DGRAM,
                               strlen(UNIX_DOMAIN_LISTEN_DGRAM));
  /* Only do this if you know it is safe to do so */
  unlink(listen_addr.addr.cun.sun_path);

  endpoint = coap_new_endpoint(context, &listen_addr, COAP_PROTO_UDP);
  if (!endpoint) {
    coap_free_context(context);
    return NULL;
  }

  /* Initialize resources - See coap_resource(3) init_resources() example */

  return context;
}
----

*CoAP Server DTLS PKI Setup*
[source, c]
----
#include <coap@LIBCOAP_API_VERSION@/coap.h>

typedef struct valid_cns_t {
  size_t count;
  char **cn_list;
} valid_cns_t;

/*
 * Common Name (CN) Callback verifier
 */
static int
verify_cn_callback(const char *cn,
                   const uint8_t *asn1_public_cert,
                   size_t asn1_length,
                   coap_session_t *c_session,
                   unsigned depth,
                   int validated,
                   void *arg) {
  valid_cns_t *valid_cn_list = (valid_cns_t *)arg;
  size_t i;
  /* Remove (void) definition if variable is used */
  (void)asn1_public_cert;
  (void)asn1_length;
  (void)c_session;
  (void)depth;
  (void)validated;

  /* Check that the CN is valid */
  for (i = 0; i < valid_cn_list->count; i++) {
    if (!strcasecmp(cn, valid_cn_list->cn_list[i])) {
      return 1;
    }
  }
  return 0;
}

typedef struct sni_def_t {
  char *sni;
  coap_dtls_key_t key;
} sni_def_t;

typedef struct valid_snis_t {
  size_t count;
  sni_def_t *sni_list;
} valid_snis_t;

/*
 * Subject Name Identifier (SNI) callback verifier
 */
static coap_dtls_key_t *
verify_pki_sni_callback(const char *sni,
                        void *arg) {
  valid_snis_t *valid_sni_list = (valid_snis_t *)arg;
  size_t i;

  /* Check that the SNI is valid */
  for (i = 0; i < valid_sni_list->count; i++) {
    if (!strcasecmp(sni, valid_sni_list->sni_list[i].sni)) {
      return &valid_sni_list->sni_list[i].key;
    }
  }
  return NULL;
}

/*
 * Set up PKI encryption information
 */
static coap_context_t *
setup_server_context_pki(const char *public_cert_file,
                         const char *private_key_file,
                         const char *ca_file,
                         valid_cns_t *valid_cn_list,
                         valid_snis_t *valid_sni_list) {
  coap_endpoint_t *endpoint;
  coap_address_t listen_addr;
  coap_dtls_pki_t dtls_pki;
  coap_context_t *context;

  /* See coap_tls_library(3) */
  if (!coap_dtls_is_supported())
    return NULL;

  context = coap_new_context(NULL);
  if (!context)
    return NULL;
  /* See coap_block(3) */
  coap_context_set_block_mode(context,
                              COAP_BLOCK_USE_LIBCOAP | COAP_BLOCK_SINGLE_BODY);


  memset(&dtls_pki, 0, sizeof(dtls_pki));

  /* see coap_encryption(3) */
  dtls_pki.version                 = COAP_DTLS_PKI_SETUP_VERSION;
  dtls_pki.verify_peer_cert        = 1;
  dtls_pki.check_common_ca         = 1;
  dtls_pki.allow_self_signed       = 1;
  dtls_pki.allow_expired_certs     = 1;
  dtls_pki.cert_chain_validation   = 1;
  dtls_pki.cert_chain_verify_depth = 1;
  dtls_pki.check_cert_revocation   = 1;
  dtls_pki.allow_no_crl            = 1;
  dtls_pki.allow_expired_crl       = 1;
  dtls_pki.allow_bad_md_hash       = 0;
  dtls_pki.allow_short_rsa_length  = 0;
  dtls_pki.is_rpk_not_cert         = 0; /* Set to 1 if RPK */
  dtls_pki.validate_cn_call_back   = verify_cn_callback;
  dtls_pki.cn_call_back_arg        = valid_cn_list;
  dtls_pki.validate_sni_call_back  = verify_pki_sni_callback;
  dtls_pki.sni_call_back_arg       = valid_sni_list;
  dtls_pki.additional_tls_setup_call_back = NULL;
  dtls_pki.client_sni              = NULL;
  dtls_pki.pki_key.key_type        = COAP_PKI_KEY_PEM;
  dtls_pki.pki_key.key.pem.ca_file = ca_file;
  dtls_pki.pki_key.key.pem.public_cert = public_cert_file;
  dtls_pki.pki_key.key.pem.private_key = private_key_file;

  if (coap_context_set_pki(context, &dtls_pki)) {
    coap_free_context(context);
    return NULL;
  }

  /* See coap_address(3) */
  coap_address_init(&listen_addr);
  listen_addr.addr.sa.sa_family = AF_INET;
  listen_addr.addr.sin.sin_port = htons(5684);

  endpoint = coap_new_endpoint(context, &listen_addr, COAP_PROTO_DTLS);
  if (!endpoint) {
    coap_free_context(context);
    return NULL;
  }

  /* Initialize resources - See coap_resource(3) init_resources() example */

  return context;
}
----

*CoAP Server DTLS PSK Setup*
[source, c]
----
#include <coap@LIBCOAP_API_VERSION@/coap.h>

typedef struct id_def_t {
  char *id;
  coap_bin_const_t key;
} id_def_t;

typedef struct valid_ids_t {
  int count;
  id_def_t *id_list;
} valid_ids_t;

/*
 * PSK Identity Pre-Shared Key selection Callback function
 */
static const coap_bin_const_t *
verify_id_callback(coap_bin_const_t *identity,
                   coap_session_t *c_session,
                   void *arg) {
  valid_ids_t *valid_id_list = (valid_ids_t *)arg;
  int i;
  /* Remove (void) definition if variable is used */
  (void)c_session;

  /* Check that the Identity is valid */
  for (i = 0; i < valid_id_list->count; i++) {
    if (!strcasecmp((const char *)identity->s, valid_id_list->id_list[i].id)) {
      return &valid_id_list->id_list[i].key;
    }
  }
  return NULL;
}

typedef struct sni_psk_def_t {
  char *sni;
  coap_dtls_spsk_info_t psk_info;
} sni_psk_def_t;

typedef struct valid_psk_snis_t {
  int count;
  sni_psk_def_t *sni_list;
} valid_psk_snis_t;

/*
 * PSK Subject Name Identifier (SNI) callback verifier
 */
static const coap_dtls_spsk_info_t *
verify_psk_sni_callback(const char *sni,
                        coap_session_t *c_session,
                        void *arg) {
  valid_psk_snis_t *valid_sni_list = (valid_psk_snis_t *)arg;
  int i;
  /* Remove (void) definition if variable is used */
  (void)c_session;

  /* Check that the SNI is valid */
  for (i = 0; i < valid_sni_list->count; i++) {
    if (!strcasecmp(sni, valid_sni_list->sni_list[i].sni)) {
      return &valid_sni_list->sni_list[i].psk_info;
    }
  }
  return NULL;
}

static coap_context_t *
setup_server_context_psk(const char *hint,
                         const uint8_t *key,
                         unsigned int key_len,
                         valid_ids_t *valid_id_list,
                         valid_psk_snis_t *valid_sni_list) {
  coap_endpoint_t *endpoint;
  coap_address_t listen_addr;
  coap_context_t *context;
  coap_dtls_spsk_t dtls_psk;

  /* See coap_tls_library(3) */
  if (!coap_dtls_is_supported())
    return NULL;

  context = coap_new_context(NULL);
  if (!context)
    return NULL;
  /* See coap_block(3) */
  coap_context_set_block_mode(context,
                              COAP_BLOCK_USE_LIBCOAP | COAP_BLOCK_SINGLE_BODY);


  memset(&dtls_psk, 0, sizeof(dtls_psk));

  /* see coap_encryption(3) */
  dtls_psk.version                 = COAP_DTLS_SPSK_SETUP_VERSION;
  dtls_psk.validate_id_call_back   = verify_id_callback;
  dtls_psk.id_call_back_arg        = valid_id_list;
  dtls_psk.validate_sni_call_back  = verify_psk_sni_callback;
  dtls_psk.sni_call_back_arg       = valid_sni_list;
  dtls_psk.psk_info.hint.s         = (const uint8_t *)hint;
  dtls_psk.psk_info.hint.length    = hint ? strlen(hint) : 0;
  dtls_psk.psk_info.key.s          = key;
  dtls_psk.psk_info.key.length     = key_len;

  if (coap_context_set_psk2(context, &dtls_psk)) {
    coap_free_context(context);
    return NULL;
  }

  /* See coap_address(3) */
  coap_address_init(&listen_addr);
  listen_addr.addr.sa.sa_family = AF_INET;
  listen_addr.addr.sin.sin_port = htons(5684);

  endpoint = coap_new_endpoint(context, &listen_addr, COAP_PROTO_DTLS);
  if (!endpoint) {
    coap_free_context(context);
    return NULL;
  }

  /* Initialize resources - See coap_resource(3) init_resources() example */

  return context;
}
----

SEE ALSO
--------
*coap_address*(3), *coap_block*(3), *coap_context*(3), *coap_encryption*(3),
*coap_endpoint_client*(3), *coap_resource*(3), *coap_session*(3) and
*coap_tls_library*(3)

FURTHER INFORMATION
-------------------
See

"https://rfc-editor.org/rfc/rfc7252[RFC7252: The Constrained Application Protocol (CoAP)]"

"https://rfc-editor.org/rfc/rfc8323[RFC8323: CoAP (Constrained Application Protocol) over TCP, TLS, and WebSockets]"

for further information.

BUGS
----
Please raise an issue on GitHub at
https://github.com/obgm/libcoap/issues to report any bugs.

Please raise a Pull Request at https://github.com/obgm/libcoap/pulls
for any fixes.

AUTHORS
-------
The libcoap project <<EMAIL>>
