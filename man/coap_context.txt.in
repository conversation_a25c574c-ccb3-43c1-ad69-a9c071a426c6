// -*- mode:doc; -*-
// vim: set syntax=asciidoc tw=0

coap_context(3)
===============
:doctype: manpage
:man source:   coap_context
:man version:  @PACKAGE_VERSION@
:man manual:   libcoap Manual

NAME
----
coap_context,
coap_new_context,
coap_free_context,
coap_context_set_max_idle_sessions,
coap_context_get_max_idle_sessions,
coap_context_set_max_handshake_sessions,
coap_context_get_max_handshake_sessions,
coap_context_set_session_timeout,
coap_context_get_session_timeout,
coap_context_set_csm_timeout_ms,
coap_context_get_csm_timeout_ms,
coap_context_set_max_token_size,
coap_context_set_app_data,
coap_context_get_app_data,
coap_context_set_cid_tuple_change
- Work with CoAP contexts

SYNOPSIS
--------
*#include <coap@LIBCOAP_API_VERSION@/coap.h>*

*coap_context_t *coap_new_context(const coap_address_t *_listen_addr_);*

*void coap_free_context(coap_context_t *_context_);*

*void coap_context_set_max_idle_sessions(coap_context_t *_context_,
unsigned int _max_idle_sessions_);*

*unsigned int coap_context_get_max_idle_sessions(
const coap_context_t *_context_);*

*void coap_context_set_max_handshake_sessions(coap_context_t *_context_,
unsigned int _max_handshake_sessions_);*

*unsigned int coap_context_get_max_handshake_sessions(
const coap_context_t *_context_);*

*void coap_context_set_session_timeout(coap_context_t *_context_,
unsigned int _session_timeout_);*

*unsigned int coap_context_get_session_timeout(
const coap_context_t *_context_);*

*void coap_context_set_csm_timeout_ms(coap_context_t *_context_,
unsigned int _csm_timeout_ms_);*

*unsigned int coap_context_get_csm_timeout_ms(const coap_context_t *_context_);*

*void coap_context_set_max_token_size(coap_context_t *_context_,
size_t _max_token_size_);*

*void coap_context_set_app_data(coap_context_t *_context_, void *_app_data_);*

*void *coap_context_get_app_data(const coap_context_t *_context_);*

*int coap_context_set_cid_tuple_change(coap_context_t *_context_context, uint8_t _every_);*

For specific (D)TLS library support, link with
*-lcoap-@LIBCOAP_API_VERSION@-notls*, *-lcoap-@LIBCOAP_API_VERSION@-gnutls*,
*-lcoap-@LIBCOAP_API_VERSION@-openssl*, *-lcoap-@LIBCOAP_API_VERSION@-mbedtls*,
*-lcoap-@LIBCOAP_API_VERSION@-wolfssl*
or *-lcoap-@LIBCOAP_API_VERSION@-tinydtls*.   Otherwise, link with
*-lcoap-@LIBCOAP_API_VERSION@* to get the default (D)TLS library support.

DESCRIPTION
-----------
This man page focuses on the CoAP Context and how to update or get information
from the opaque coap_context_t structure.

The CoAP stack's global state is stored in a coap_context_t Context object.
Resources, Endpoints and Sessions are associated with this context object.
There can be more than one coap_context_t object per application, it is up to
the application to manage each one accordingly.

FUNCTIONS
---------

*Function: coap_new_context()*

The *coap_new_context*() function creates a new Context that is then used
to keep all the CoAP Resources, Endpoints and Sessions information.
The optional _listen_addr_ parameter, if set for a CoAP server, creates an
Endpoint that is added to the _context_ that is listening for un-encrypted
traffic on the IP address and port number defined by _listen_addr_.

*Function: coap_free_context()*

The *coap_free_context*() function must be used to release the CoAP stack
_context_.  It clears all entries from the receive queue and send queue and
deletes the Resources that have been registered with _context_, and frees the
attached Sessions and Endpoints.

*WARNING:* It is unsafe to call *coap_free_context*() in an atexit() handler
as other libraries may also call atexit() and clear down some CoAP
required functionality.

*WARNING:* It is unsafe to call *coap_free_context*() in any libcoap callback
handlers as set up in *coap_handler*(3).

*WARNING:* In a multi-thread, libcoap thread-safe environment, if other threads
are using _context_, it is not recommended to call *coap_free_context*() as
various libcoap APIs will unexpectedly fail. It is best to clear down those other
threads first before calling *coap_free_context*().

*Function: coap_context_set_max_idle_sessions()*

The *coap_context_set_max_idle_sessions*() function sets the maximum number of
idle server sessions to _max_idle_sessions_ for _context_.  If this number is
exceeded, the least recently used server session is completely removed. 0 (the
initial default) means that the number of idle sessions is not monitored.

*Function: coap_context_get_max_idle_sessions()*

The *coap_context_get_max_idle_sessions*() function returns the maximum number
of idle server sessions for _context_.

*Function: coap_context_set_max_handshake_sessions()*

The *coap_context_set_max_handshake_sessions*() function sets the maximum
number of outstanding server sessions in (D)TLS handshake to
_max_handshake_sessions_ for _context_.  If this number is exceeded, the least
recently used server session in handshake is completely removed. 0 (the default)
means that the number of handshakes is not monitored.

*Function: coap_context_get_max_handshake_sessions()*

The *coap_context_get_max_handshake_sessions*() function returns the maximum
number of outstanding server sessions in (D)TLS handshake for _context_.

*Function: coap_context_set_session_timeout()*

The *coap_context_set_session_timeout*() function sets the number of seconds of
inactivity to _session_timeout_ for _context_ before an idle server session is
removed. 0 (the default) means wait for the default of 300 seconds.

*Function: coap_context_get_session_timeout()*

The *coap_context_get_session_timeout*() function returns the seconds to wait
before timing out an idle server session for _context_.

*Function: coap_context_set_csm_timeout_ms()*

The *coap_context_set_csm_timeout_ms*() function sets the number of milliseconds
to wait for a (TCP) CSM negotiation response from the peer to _csm_timeout_ms_
for _context_ before timing out and assuming CoAP server is 'broken'. The
default is 1000 milliseconds. The minimum value for _csm_timeout_ms_ is set to
10 milliseconds and the maximum value for _csm_timeout_ms_ is set to 10000
milliseconds.

*Function: coap_context_get_csm_timeout_ms()*

The *coap_context_get_csm_timeout_ms*() function returns the milliseconds to wait
for a (TCP) CSM negotiation response from the peer for _context_,

*Function: coap_context_set_max_token_size()*

The *coap_context_set_max_token_size*() function sets the _max_token_size_
for _context_.  _max_token_size_ must be greater than 8 to indicate
support for https://rfc-editor.org/rfc/rfc8974[RFC8974] up to _max_token_size_
bytes, else 8 to disable https://rfc-editor.org/rfc/rfc8974[RFC8974]
(if previously set).

*NOTE:* For the client, it will send an initial PDU to test the server
supports the requested extended token size as per
"https://rfc-editor.org/rfc/rfc8974.html#section-2.2.2[RFC8794 Section 2.2.2]"

*Function: coap_context_set_app_data()*

The *coap_context_set_app_data*() function is used to define a _app_data_
pointer for the _context_ which can then be retrieved at a later date.

*Function: coap_context_get_app_data()*

The *coap_context_get_app_data*() function is used to retrieve the app_data
pointer previously defined by *coap_context_set_app_data*().

*Function: coap_context_set_cid_tuple_change()*

The *coap_context_set_cid_tuple_change*() function is used to define to a client
to force the client's port to change _every_ packets sent, providing the ability
to test a CID (RFC9146) enabled server. Only supported by DTLS libraries that
support CID.

RETURN VALUES
-------------
*coap_new_context*() returns a newly created context or
NULL if there is a creation failure.

*coap_context_get_max_idle_sessions*() returns the maximum number of idle
server sessions.

*coap_context_get_max_handshake_sessions*() returns the maximum number of
outstanding server sessions in (D)TLS handshake.

*coap_context_get_session_timeout*() returns the seconds to wait before timing
out an idle server session.

*coap_context_get_csm_timeout_ms*() returns the milliseconds to wait for a
(TCP) CSM negotiation response from the peer.

*coap_context_get_app_data*() returns a previously defined pointer.

*coap_context_set_cid_tuple_change*() returns 1 on success, else 0;

SEE ALSO
--------
*coap_session*(3)

FURTHER INFORMATION
-------------------
See

"https://rfc-editor.org/rfc/rfc7252[RFC7252: The Constrained Application Protocol (CoAP)]"

"https://rfc-editor.org/rfc/rfc8974[RFC8974: Extended Tokens and Stateless Clients in the Constrained Application Protocol (CoAP)]"

for further information.

BUGS
----
Please raise an issue on GitHub at
https://github.com/obgm/libcoap/issues to report any bugs.

Please raise a Pull Request at https://github.com/obgm/libcoap/pulls
for any fixes.

AUTHORS
-------
The libcoap project <<EMAIL>>
