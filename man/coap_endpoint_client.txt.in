// -*- mode:doc; -*-
// vim: set syntax=asciidoc tw=0

coap_endpoint_client(3)
=======================
:doctype: manpage
:man source:   coap_endpoint_client
:man version:  @PACKAGE_VERSION@
:man manual:   libcoap Manual

NAME
----
coap_endpoint_client,
coap_new_client_session,
coap_new_client_session_psk2,
coap_new_client_session_pki,
coap_session_set_mtu,
coap_session_max_pdu_size,
coap_mcast_set_hops
- Work with CoAP client endpoints

SYNOPSIS
--------
*#include <coap@LIBCOAP_API_VERSION@/coap.h>*

*coap_session_t *coap_new_client_session(coap_context_t *_context_,
const coap_address_t *_local_if_, const coap_address_t *_server_,
coap_proto_t _proto_);*

*coap_session_t *coap_new_client_session_psk2(coap_context_t *_context_,
const coap_address_t *_local_if_, const coap_address_t *_server_, coap_proto_t
_proto_, coap_dtls_cpsk_t *_setup_data_);*

*coap_session_t *coap_new_client_session_pki(coap_context_t *_context_,
const coap_address_t *_local_if_, const coap_address_t *_server_, coap_proto_t
_proto_, coap_dtls_pki_t *_setup_data_);*

*void coap_session_set_mtu(coap_session_t *_session_, unsigned _mtu_);*

*size_t coap_session_max_pdu_size(const coap_session_t *_session_);*

*int coap_mcast_set_hops(coap_session_t *_session_, size_t _hops_);*

For specific (D)TLS library support, link with
*-lcoap-@LIBCOAP_API_VERSION@-notls*, *-lcoap-@LIBCOAP_API_VERSION@-gnutls*,
*-lcoap-@LIBCOAP_API_VERSION@-openssl*, *-lcoap-@LIBCOAP_API_VERSION@-mbedtls*,
*-lcoap-@LIBCOAP_API_VERSION@-wolfssl*
or *-lcoap-@LIBCOAP_API_VERSION@-tinydtls*.   Otherwise, link with
*-lcoap-@LIBCOAP_API_VERSION@* to get the default (D)TLS library support.

DESCRIPTION
-----------
This man page focuses on the setting up of a CoAP client endpoint and hence
creation of a CoAP _session_ used to connect to a server.  For a CoAP server
endpoint, see *coap_endpoint_server*(3). There is no need to call
*coap_new_endpoint*(3) for a client as well as one of the
*coap_new_client_server**() functions.

The CoAP stack's global state is stored in a coap_context_t _context_ object.
Resources, Endpoints and Sessions are associated with this _context_ object.
There can be more than one coap_context_t object per application, it is up to
the application to manage each one accordingly.

A CoAP _session_ maintains the state of an ongoing connection between a Client
and Server which is stored in a coap_session_t _session_ object. A CoAP
_session_ is tracked by local port, CoAP protocol, remote IP address and
remote port, or in the case of Unix Domain sockets, the local path and the
remote path.

The _session_ network traffic can be encrypted or un-encrypted if there is an
underlying TLS library.

If (D)TLS is going to be used for encrypting the network traffic, then the
(D)TLS
information for Pre-Shared Keys (PSK) or Public Key Infrastructure (PKI) needs
to be configured before any network traffic starts to flow. For Clients, this
is done during the Client _session_ set up.

For Clients, all the encryption information can be held at the (D)TLS
context and CoAP _context_ levels, or at the (D)TLS session and CoAP
_session_ levels.  If defined at the _context_ level, then when a _session_ is
created, it will inherit the _context_ definitions, unless they have separately
been defined for the _session_ level, in which case the _session_ version will
get used.  Typically the information will be configured at the _session_ level
for Clients.

In principle the set-up sequence for CoAP client endpoints looks like
----
coap_new_context()
coap_context_set_pki_root_cas() - if the root CAs need to be updated and using PKI
coap_new_client_session(), coap_new_client_session_pki() or coap_new_client_session_psk2()
----

Multiple client endpoints and hence sessions are supported per _context_.

Different CoAP protocols can be defined for _proto_ - the current supported
list is:

[source, c]
----
COAP_PROTO_UDP
COAP_PROTO_DTLS
COAP_PROTO_TCP
COAP_PROTO_TLS
COAP_PROTO_WS
COAP_PROTO_WSS
----

*coap_tcp_is_supported*(3), *coap_dtls_is_supported*(3),
*coap_tls_is_supported*(3), *coap_ws_is_supported*(3) and
*coap_wss_is_supported*(3) can be used for checking whether the underlying
TCP, (D)TLS or WebSocket protocol support is available.
See *coap_tls_library(3)* for further information on the types of (D)TLS
sessions supported.

Libcoap supports 3 different socket types:

[source, c]
----
AF_INET  IPv4 IP addresses and ports
AF_INET6 IPv6 IP addresses and ports and can be dual IPv4/IPv6 stacked
AF_UNIX  Unix Domain using file path names
----

For AF_INET and AF_INET6, the client does not need to specify a local IP
address and/or port as default values will get filled in. However for AF_UNIX,
the local pathname must be provided and must be unique per client session. This
unique local pathname will get deleted on the session being properly closed at
application exit.

The client must specify IP and port when defining the *coap_address_t* (see
*coap_address_t*(3)) for the remote end of the session if AF_INET or AF_INET6.
If port is 0, then the default CoAP port is used instead.  If AF_UNIX, the
unix domain path to connect to must be specified.

FUNCTIONS
---------

*Function: coap_new_client_session()*

The *coap_new_client_session*() function creates a client endpoint for a
specific _context_ and initiates a new client session to the specified
_server_ using the CoAP protocol _proto_ as defined above. If the port is set
to 0 in _server_ (for AF_INET or AF_INET6), then the default CoAP port is used.

Normally _local_if_ would be set to NULL, but by specifying
_local_if_ the source of the network session can be bound to a specific IP
address or port. For AF_UNIX, _local_if_ must be specified pointing to an
appropriate *coap_address_t*.  If _local_if_ is defined, the address families
for _local_if_ and _server_ must be identical. The session will initially have
a reference count of 1.

To stop using a client session, the reference count must be decremented to 0
by calling *coap_session_release*(3). See *coap_session*(3). This will remove
the client endpoint's _session_ and all its associated information.

*Function: coap_new_client_session_pki()*

The *coap_new_client_session_pki*() function, for a specific _context_, is
used to configure the (D)TLS context using the _setup_data_ variables as defined
in the coap_dtls_pki_t structure in the newly created endpoint session -
see *coap_encryption*(3). The connection is to the specified _server_ using
the CoAP protocol _proto_ as defined above.  If the port is set to 0 in
_server_ (for AF_INET or AF_INET6), then the default CoAP port is used.

Normally _local_if_ would be set to NULL, but by specifying
_local_if_ the source of the network session can be bound to a specific IP
address or port. For AF_UNIX, _local_if_ must be specified pointing to an
appropriate *coap_address_t*.  If _local_if_ is defined, the address families
for _local_if_ and _server_ must be identical. The session will initially have
a reference count of 1.

To stop using a client session, the reference count must be decremented to 0
by calling *coap_session_release*(3). See *coap_session*(3). This will remove
the client endpoint's _session_ and all its associated information.

*Function: coap_new_client_session_psk2()*

The *coap_new_client_session_psk2*() function, for a specific _context_, is
used to configure the (D)TLS context using the _setup_data_ variables as defined
in the coap_dtls_cpsk_t structure in the newly created endpoint session -
see *coap_encryption*(3). The connection is to the specified _server_ using
the CoAP protocol _proto_ as defined above.  If the port is set to 0 in
_server_ (for AF_INET or AF_INET6), then the default CoAP port is used.

Normally _local_if_ would be set to NULL, but by specifying
_local_if_ the source of the network session can be bound to a specific IP
address or port. For AF_UNIX, _local_if_ must be specified pointing to an
appropriate *coap_address_t*.  If _local_if_ is defined, the address families
for _local_if_ and _server_ must be identical. The session will initially have
a reference count of 1.

To stop using a client session, the reference count must be decremented to 0
by calling *coap_session_release*(3). See *coap_session*(3). This will remove
the client endpoint's _session_ and all its associated information.

*Function: coap_session_set_mtu()*

The *coap_session_set_mtu*() function is used to set the MTU size
(the maximum message size) of the data in a packet, excluding any IP or
TCP/UDP overhead to _mtu_ for the client endpoint's _session_.  The default
MTU is 1152.

*Function: coap_session_max_pdu_size()*

The *coap_session_max_pdu_size*() function is used to get the maximum MTU
size of the data for the client endpoint's _session_.

*Function: coap_mcast_set_hops()*

The *coap_mcast_set_hops*() function is used to set the maximum number
of _hops_ (ttl) that a transmitted multicast packet can pass through before
getting dropped (default is 1, so that the ttl expires after decrementing
if the packet is trying to pass out of the local network) for the specified
_session_.

RETURN VALUES
-------------
*coap_new_client_session*(), *coap_new_client_session_psk2*(),
*coap_new_client_session_pki*() return a newly created client.
session or NULL if there is a creation failure.

*coap_session_max_pdu_size*() returns the MTU size.

*coap_mcast_set_hops*() returns 1 on success, else 0 on failure.

EXAMPLES
--------
*CoAP Client Non-Encrypted Setup*
[source, c]
----
#include <coap@LIBCOAP_API_VERSION@/coap.h>

#include <netinet/in.h>

static coap_session_t *
setup_client_session(struct in_addr ip_address) {
  coap_session_t *session;
  coap_address_t server;
  /* See coap_context(3) */
  coap_context_t *context = coap_new_context(NULL);

  if (!context)
    return NULL;
  /* See coap_block(3) */
  coap_context_set_block_mode(context,
                              COAP_BLOCK_USE_LIBCOAP | COAP_BLOCK_SINGLE_BODY);


  /* See coap_address(3) */
  coap_address_init(&server);
  server.addr.sa.sa_family = AF_INET;
  server.addr.sin.sin_addr = ip_address;
  server.addr.sin.sin_port = htons(5683);

  session = coap_new_client_session(context, NULL, &server, COAP_PROTO_UDP);
  if (!session) {
    coap_free_context(context);
    return NULL;
  }
  /* The context is in session->context */
  return session;
}
----

*CoAP Client Non-Encrypted Unix Domain Setup*
[source, c]
----
#include <coap@LIBCOAP_API_VERSION@/coap.h>

#include <stdio.h>
#include <sys/types.h>
#include <unistd.h>

static coap_session_t *
setup_client_session(const char *server_ud) {
  coap_session_t *session;
  coap_address_t server;
  coap_address_t local;
  /* See coap_context(3) */
  coap_context_t *context = coap_new_context(NULL);

  if (!context)
    return NULL;
  /* See coap_block(3) */
  coap_context_set_block_mode(context,
                              COAP_BLOCK_USE_LIBCOAP | COAP_BLOCK_SINGLE_BODY);


  /* See coap_address(3) */
  coap_address_init(&server);
  server.addr.sa.sa_family = AF_UNIX;
  snprintf(server.addr.cun.sun_path, sizeof(server.addr.cun.sun_path),
           "%s", server_ud);

  /* Need to have a uniquely named local address */
  coap_address_init(&local);
  local.addr.sa.sa_family = AF_UNIX;
  snprintf(local.addr.cun.sun_path, sizeof(server.addr.cun.sun_path),
           "/tmp/client.%d", getpid());
  /* Only do this if you know it is safe to do so */
  unlink(local.addr.cun.sun_path);

  session = coap_new_client_session(context, &local, &server, COAP_PROTO_UDP);
  if (!session) {
    coap_free_context(context);
    return NULL;
  }
  /* The context is in session->context */
  return session;
}
----

*CoAP Client PKI Setup*
[source, c]
----
#include <coap@LIBCOAP_API_VERSION@/coap.h>

#include <stdio.h>
#include <netinet/in.h>

static int
verify_cn_callback(const char *cn,
                   const uint8_t *asn1_public_cert,
                   size_t asn1_length,
                   coap_session_t *c_session,
                   unsigned int depth,
                   int validated,
                   void *arg) {
  /* Remove (void) definition if variable is used */
  (void)cn;
  (void)asn1_public_cert;
  (void)asn1_length;
  (void)c_session;
  (void)depth;
  (void)validated;
  (void)arg;

  /* Check that the CN is valid */

  /* ... */

  return 1;
}

static char client_sni[256];

static coap_session_t *
setup_client_session_pki(const char *host,
                         struct in_addr ip_address,
                         const char *public_cert_file,
                         const char *private_key_file,
                         const char *ca_file) {
  coap_session_t *session;
  coap_address_t server;
  coap_dtls_pki_t dtls_pki;
  /* See coap_context(3) */
  coap_context_t *context = coap_new_context(NULL);

  if (!context)
    return NULL;
  /* See coap_block(3) */
  coap_context_set_block_mode(context,
                              COAP_BLOCK_USE_LIBCOAP | COAP_BLOCK_SINGLE_BODY);


  /* See coap_address(3) */
  coap_address_init(&server);
  server.addr.sa.sa_family = AF_INET;
  server.addr.sin.sin_addr = ip_address;
  server.addr.sin.sin_port = htons(5684);

  memset(&dtls_pki, 0, sizeof(dtls_pki));

  snprintf(client_sni, sizeof(client_sni), "%s", host);

  /* See coap_encryption(3) */
  dtls_pki.version                 = COAP_DTLS_PKI_SETUP_VERSION;
  dtls_pki.verify_peer_cert        = 1;
  dtls_pki.check_common_ca         = 1;
  dtls_pki.allow_self_signed       = 1;
  dtls_pki.allow_expired_certs     = 1;
  dtls_pki.cert_chain_validation   = 1;
  dtls_pki.cert_chain_verify_depth = 1;
  dtls_pki.check_cert_revocation   = 1;
  dtls_pki.allow_no_crl            = 1;
  dtls_pki.allow_expired_crl       = 1;
  dtls_pki.allow_bad_md_hash       = 0;
  dtls_pki.allow_short_rsa_length  = 0;
  dtls_pki.is_rpk_not_cert         = 0; /* Set to 1 if RPK */
  dtls_pki.validate_cn_call_back   = verify_cn_callback;
  dtls_pki.cn_call_back_arg        = NULL;
  dtls_pki.validate_sni_call_back  = NULL;
  dtls_pki.sni_call_back_arg       = NULL;
  dtls_pki.additional_tls_setup_call_back = NULL;
  dtls_pki.client_sni              = client_sni;
  dtls_pki.pki_key.key_type        = COAP_PKI_KEY_PEM;
  dtls_pki.pki_key.key.pem.ca_file = ca_file;
  dtls_pki.pki_key.key.pem.public_cert = public_cert_file;
  dtls_pki.pki_key.key.pem.private_key = private_key_file;

  session = coap_new_client_session_pki(context, NULL, &server,
                                        COAP_PROTO_DTLS, &dtls_pki);
  if (!session) {
    coap_free_context(context);
    return NULL;
  }
  /* The context is in session->context */
  return session;
}
----

*CoAP Client PSK Setup*
[source, c]
----
#include <coap@LIBCOAP_API_VERSION@/coap.h>

#include <stdio.h>
#include <netinet/in.h>

#ifndef min
#define min(a,b) ((a) < (b) ? (a) : (b))
#endif

static const coap_dtls_cpsk_info_t *
verify_ih_callback(coap_str_const_t *hint,
                   coap_session_t *c_session,
                   void *arg) {
  coap_dtls_cpsk_info_t *psk_info = (coap_dtls_cpsk_info_t *)arg;
  /* Remove (void) definition if variable is used */
  (void)c_session;

  coap_log_info("Identity Hint '%.*s' provided\n", (int)hint->length, hint->s);

  /* Just use the defined information for now as passed in by arg */
  return psk_info;
}

static coap_dtls_cpsk_t dtls_psk;
static char client_sni[256];

static coap_session_t *
setup_client_session_psk(const char *host,
                         struct in_addr ip_address,
                         const uint8_t *identity,
                         unsigned int identity_len,
                         const uint8_t *key,
                         unsigned int key_len) {
  coap_session_t *session;
  coap_address_t server;
  /* See coap_context(3) */
  coap_context_t *context = coap_new_context(NULL);

  if (!context)
    return NULL;
  /* See coap_block(3) */
  coap_context_set_block_mode(context,
                              COAP_BLOCK_USE_LIBCOAP | COAP_BLOCK_SINGLE_BODY);


  /* See coap_address(3) */
  coap_address_init(&server);
  server.addr.sa.sa_family = AF_INET;
  server.addr.sin.sin_addr = ip_address;
  server.addr.sin.sin_port = htons(5684);

  /* See coap_encryption(3) */
  memset(&dtls_psk, 0, sizeof(dtls_psk));
  dtls_psk.version = COAP_DTLS_CPSK_SETUP_VERSION;
  dtls_psk.validate_ih_call_back = verify_ih_callback;
  dtls_psk.ih_call_back_arg = &dtls_psk.psk_info;
  snprintf(client_sni, sizeof(client_sni), "%s", host);
  dtls_psk.client_sni = client_sni;
  dtls_psk.psk_info.identity.s = identity;
  dtls_psk.psk_info.identity.length = identity_len;
  dtls_psk.psk_info.key.s = key;
  dtls_psk.psk_info.key.length = key_len;
  session = coap_new_client_session_psk2(context, NULL, &server,
                                         COAP_PROTO_DTLS, &dtls_psk);
  if (!session) {
    coap_free_context(context);
    return NULL;
  }
  /* The context is in session->context */
  return session;
}
----

*CoAP Client Anonymous PKI Setup*
[source, c]
----
#include <coap@LIBCOAP_API_VERSION@/coap.h>

#include <netinet/in.h>

static coap_session_t *
setup_client_session_dtls(struct in_addr ip_address) {
  coap_session_t *session;
  coap_address_t server;
  /* See coap_context(3) */
  coap_context_t *context = coap_new_context(NULL);

  if (!context)
    return NULL;
  /* See coap_block(3) */
  coap_context_set_block_mode(context,
                              COAP_BLOCK_USE_LIBCOAP | COAP_BLOCK_SINGLE_BODY);


  /* See coap_address(3) */
  coap_address_init(&server);
  server.addr.sa.sa_family = AF_INET;
  server.addr.sin.sin_addr = ip_address;
  server.addr.sin.sin_port = htons(5683);

  session = coap_new_client_session(context, NULL, &server,
                                    COAP_PROTO_DTLS);
  if (!session) {
    coap_free_context(context);
    return NULL;
  }
  /* The context is in session->context */
  return session;
}
----

SEE ALSO
--------
*coap_address*(3), *coap_block*(3), *coap_context*(3), *coap_encryption*(3),
*coap_endpoint_server*(3), *coap_resource*(3), *coap_session*(3) and
*coap_tls_library*(3)

FURTHER INFORMATION
-------------------
See

"https://rfc-editor.org/rfc/rfc7252[RFC7252: The Constrained Application Protocol (CoAP)]"

"https://rfc-editor.org/rfc/rfc8323[RFC8323: CoAP (Constrained Application Protocol) over TCP, TLS, and WebSockets]"

for further information.

BUGS
----
Please raise an issue on GitHub at
https://github.com/obgm/libcoap/issues to report any bugs.

Please raise a Pull Request at https://github.com/obgm/libcoap/pulls
for any fixes.

AUTHORS
-------
The libcoap project <<EMAIL>>
