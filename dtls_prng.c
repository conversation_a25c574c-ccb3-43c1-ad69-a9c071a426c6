/*******************************************************************************
 *
 * Copyright (c) 2011-2019 <PERSON> (TZI) and others.
 * All rights reserved. This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License v1.0
 * and Eclipse Distribution License v. 1.0 which accompanies this distribution.
 *
 * The Eclipse Public License is available at http://www.eclipse.org/legal/epl-v10.html
 * and the Eclipse Distribution License is available at 
 * http://www.eclipse.org/org/documents/edl-v10.php.
 *
 * Contributors:
 *    <PERSON>  - initial API and implementation
 *    Hauke <PERSON> - memory optimization, ECC integration
 *    Jon Shallow    - platform dependent prng support
 *
 *******************************************************************************/

#include "tinydtls.h"

#if defined (WITH_CONTIKI)
#include "platform-specific/dtls_prng_contiki.c"

#elif defined (ESPIDF_VERSION)
#include "platform-specific/dtls_prng_espidf.c"

#elif defined (RIOT_VERSION)
#include "platform-specific/dtls_prng_riot.c"

#elif defined (WITH_ZEPHYR)
#include "platform-specific/dtls_prng_zephyr.c"

#elif defined (IS_WINDOWS)
#include "platform-specific/dtls_prng_win.c"

#elif defined (WITH_LWIP)
#include "platform-specific/dtls_prng_lwip.c"

#elif defined (WITH_POSIX)
#include "platform-specific/dtls_prng_posix.c"

#else
#error platform specific prng not defined

#endif
