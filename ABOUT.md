# About This Content

2015-12-18

# License

The Eclipse Foundation makes available all content in this plug-in
("Content"). Unless otherwise indicated below, the Content is provided
to you under the terms and conditions of the Eclipse Public License
Version 1.0 ("EPL") and Eclipse Distribution License Version 1.0
(“EDL”). A copy of the EPL is available at
http://www.eclipse.org/legal/epl-v10.html and a copy of the EDL is
available at http://www.eclipse.org/org/documents/edl-v10.php.  For
purposes of the EPL, "Program" will mean the Content.

If you did not receive this Content directly from the Eclipse
Foundation, the Content is being redistributed by another party
("Redistributor") and different terms and conditions may apply to your
use of any object code in the Content. Check the Redistributor’s
license that was provided with the Content. If no such license exists,
contact the Redistributor. Unless otherwise indicated below, the terms
and conditions of the EPL and EDL still apply to any source code in
the Content and such source code may be obtained at
http://www.eclipse.org.

# Third Party Content

The Content includes items that have been sourced from third parties
as set out below. If you did not receive this Content directly from
the Eclipse Foundation, the following is provided for informational
purposes only, and you should look to the Redistributor’s license for
terms and conditions of use.

## Uthash 1.9.9

[Uthash](https://troydhanson.github.io/uthash/) is an implementation
of hash tables and linked lists for C structures by Troy D. Hanson.
Uthash is licensed under the BSD revised license, see
https://troydhanson.github.io/uthash/license.html,

## rijndael 3.0

The library uses an implementation of the Rijndael algorithm (AES)
from [OpenBSD](http://www.openbsd.org/) by Vincent Rijmen, Antoon
Bosselaers, and Paulo Barreto. The authors have placed the code in the
public domain under the license included in the file rijndael.h (see
http://www.openbsd.org/cgi-bin/cvsweb).

## SHA-256 1.0

The SHA-256 implementation from Aaron D. Gifford is available at
http://www.aarongifford.com/computers/sha2-1.0.1.tgz under a
3-clause BSD license.

## ECC 1.0

The implementation of the ECC curve secp256r1 was originally developed
by Chris K Cockrum and has been put under MIT license for inclusion
with tinydtls. The original source is made available in
https://cockrum.net/Implementation_of_ECC_on_an_8-bit_microcontroller.pdf
