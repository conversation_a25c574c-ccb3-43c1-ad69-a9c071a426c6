@startuml
skinparam dpi 300
skinparam ArrowFontSize 18

start
: Send CONNECT packet;
: count = 0;

repeat
	: Receive single byte;
repeat while (                      No network data available AND \n retry count < MQTT_MAX_CONNACK_RECEIVE_RETRY_COUNT) is (yes)
-> no or timeout == 0;

repeat
	: Get rest of CONNACK packet;
	note left: Retry zero byte reads for maximum period \nof **MQTT_RECV_POLLING_TIMEOUT_MS**
repeat while( Received complete packet? ) is ( no )
: Deserialize CONNACK packet;
stop

@enduml
