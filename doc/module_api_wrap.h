/* doc/module_api_wrap.h
 *
 * Copyright (C) 2021-2024 <PERSON> <<EMAIL>>
 *
 * SPDX-License-Identifier: BSD-2-Clause
 *
 * This file is part of the CoAP C library libcoap. Please see README and
 * COPYING for terms of use.
 */

/**
 * @file module_api_wrap.h
 * @brief Doxygen specific wrapper for Modules layout
 */

/**
 * @defgroup application_api Application API
 * Application API Structures, Macros, Typedefs, Enums and Functions
 * @defgroup internal_api Libcoap Internal API
 * libcoap Internal API Structures, Macros, Typedefs, Enums and Functions
 */
