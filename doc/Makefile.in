# the library's version
VERSION:=@PACKAGE_VERSION@
PACKAGE_TARNAME:=@PACKAGE_TARNAME@

# tools
@SET_MAKE@
SHELL = /bin/sh
MKDIR = mkdir
DOXYGEN= @DOXYGEN@

top_builddir = @top_builddir@
prefix = @prefix@
datarootdir = @datarootdir@
docdir = @docdir@
htmldir = @htmldir@

DISTDIR?=$(top_builddir)/@PACKAGE_TARNAME@-@PACKAGE_VERSION@
FILES:=Makefile.in Doxyfile.in html

doc:	Doxyfile
	$(DOXYGEN) -l
	$(DOXYGEN) $< >./doxygen.out

clean:
	@rm -rf html

distclean:	clean
	@rm -rf $(DISTDIR)
	@rm -f *~ 

dist:	doc
	test -d $(DISTDIR)/doc || mkdir $(DISTDIR)/doc
	cp -r $(FILES) $(DISTDIR)/doc

install:	$(doc) html
	test -d $(DESTDIR)$(htmldir) || mkdir -p $(DESTDIR)$(htmldir)
	cp -r html/* $(DESTDIR)$(htmldir)

uninstall:
	for file in $(DESTDIR)$(htmldir)/* $(DESTDIR)$(htmldir)/search/*; do \
		if test -f $$file ; then $(RM) $$file ; fi ;\
	done
	if test -d $(DESTDIR)$(htmldir)/search ; then $(RMDIR) $(DESTDIR)$(htmldir)/search ; fi
	if test -d $(DESTDIR)$(htmldir) ; then $(RMDIR) $(DESTDIR)$(htmldir) ; fi
