.config
*.o
*.pyc

# gtags
GTAGS
GRTAGS
GPATH

# emacs
.dir-locals.el

# emacs temp file suffixes
*~
.#*
\#*#

# eclipse setting
.settings

# MacOS directory files
.DS_Store

# Components Unit Test Apps files
components/**/build
components/**/sdkconfig
components/**/sdkconfig.old

# Example project files
examples/**/sdkconfig
examples/**/sdkconfig.old
examples/**/build

# gcov coverage reports
*.gcda
*.gcno
coverage.info
coverage_report/

# VS Code Settings
.vscode/

# VIM files
*.swp
*.swo

# Clion IDE CMake build & config
.idea/
cmake-build-*/

# Results for the checking of the Python coding style and static analysis
.mypy_cache
flake8_output.txt

# ESP-IDF default build directory name
build

# lock files for examples and components
dependencies.lock
.cache/
.lvimrc
compile_commands.json
tmp
managed_components

# Docs build output
docs/_build
