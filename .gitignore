# .gitignore for libcoap

# autosave files
*~
\#*#

# ignoring autogenerated files and directories by autoreconf
INSTALL
/Makefile
Makefile.in
aclocal.m4
ar-lib
autom4te.cache/
coap_config.h
/coap_config.h.in
compile
config.*
!config.yml
configure
debian/
depcomp
install-sh
libcoap-*.tar.bz2
libtool
ltmain.sh
m4/libtool.m4
m4/ltoptions.m4
m4/ltsugar.m4
m4/ltversion.m4
m4/lt~obsolete.m4
missing
stamp-h1

# ignoring more files generated by the configure script or the make actions
.libs/
libcoap*.la
libcoap*.pc
src/**/.deps/
src/**/.dirstamp
src/**/.libs/
src/**/*.o
src/**/*.lo
src/*.lo
src/*.o
src/.deps/
src/.dirstamp
src/.libs/
build/

# the doc/ folder
doc/Doxyfile
doc/Makefile
doc/Makefile.in
doc/docbook-xsl.css
doc/doxyfile.stamp
doc/doxygen_sqlite3.db
doc/DoxygenLayout.xml
doc/upgrade_*.html
doc/html/
doc/man_html/
doc/man_tmp/

# the man/ folder
man/docbook-xsl.css
man/examples-code-check
man/examples-code-check.exe
man/examples-code-check.o
man/Makefile
man/Makefile.in
man/tmp
man/.deps/
man/*.html
man/*.txt
man/*.xml
man/*.3
man/*.5
man/*.7

# the examples/ folder
examples/.deps/
examples/Makefile
examples/*.o
examples/coap-client
examples/coap-client-*
examples/coap-etsi_iot_01
examples/coap-rd
examples/coap-rd-*
examples/coap-server
examples/coap-server-*
examples/coap-tiny
examples/oscore-interop-server
examples/*.exe
examples/riot/pkg_libcoap/patches

# the include/ folder
include/coap3/coap_defines.h

# the tests/ folder
tests/.deps
tests/Makefile
tests/oss-fuzz/Makefile.ci
tests/testdriver
tests/*.o
tests/test_common.h

# the cmake/ folder
cmake/libcoap

# the scripts/ folder
scripts/fix_version.sh

# ctags - Sublime plugin
tags
.tags*
TAGS

# ignore gcov-generated files
**/*.gcda
**/*.gcno
**/*.gcov

# IDE files
CMakeLists.txt.user
/.vs/
/out/
/.vscode/
/_build/
